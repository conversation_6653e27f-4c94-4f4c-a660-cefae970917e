import win32gui
import win32con

def enum_windows_callback(hwnd, windows):
    """枚举窗口的回调函数"""
    if win32gui.IsWindowVisible(hwnd):
        window_text = win32gui.GetWindowText(hwnd)
        class_name = win32gui.GetClassName(hwnd)
        
        # 只显示有标题的窗口
        if window_text:
            windows.append({
                'hwnd': hwnd,
                'title': window_text,
                'class': class_name
            })

def find_mumu_windows():
    """查找MuMu模拟器相关的窗口"""
    print("正在搜索MuMu模拟器窗口...")
    print("=" * 60)
    
    windows = []
    win32gui.EnumWindows(enum_windows_callback, windows)
    
    mumu_windows = []
    qt_windows = []
    
    for window in windows:
        title = window['title'].lower()
        class_name = window['class'].lower()
        
        # 查找包含MuMu关键词的窗口
        if 'mumu' in title or 'nemuwin' in class_name or 'qt5156qwindowicon' in class_name:
            mumu_windows.append(window)
            
        # 查找Qt相关窗口
        if 'qt' in class_name:
            qt_windows.append(window)
    
    print("找到的MuMu相关窗口:")
    print("-" * 40)
    for i, window in enumerate(mumu_windows, 1):
        print(f"{i}. 句柄: {window['hwnd']}")
        print(f"   标题: {window['title']}")
        print(f"   类名: {window['class']}")
        print()
    
    if not mumu_windows:
        print("未找到MuMu相关窗口，显示所有Qt窗口:")
        print("-" * 40)
        for i, window in enumerate(qt_windows, 1):
            print(f"{i}. 句柄: {window['hwnd']}")
            print(f"   标题: {window['title']}")
            print(f"   类名: {window['class']}")
            print()
    
    return mumu_windows if mumu_windows else qt_windows

def find_child_windows(parent_hwnd):
    """查找指定窗口的子窗口"""
    child_windows = []
    
    def enum_child_callback(hwnd, windows):
        window_text = win32gui.GetWindowText(hwnd)
        class_name = win32gui.GetClassName(hwnd)
        windows.append({
            'hwnd': hwnd,
            'title': window_text,
            'class': class_name
        })
    
    try:
        win32gui.EnumChildWindows(parent_hwnd, enum_child_callback, child_windows)
    except:
        pass
    
    return child_windows

def main():
    """主函数"""
    print("MuMu模拟器窗口句柄检测工具")
    print("=" * 60)
    
    # 查找MuMu窗口
    mumu_windows = find_mumu_windows()
    
    if not mumu_windows:
        print("未找到任何相关窗口，请确保MuMu模拟器正在运行。")
        return
    
    # 让用户选择主窗口
    print("请选择MuMu模拟器的主窗口:")
    for i, window in enumerate(mumu_windows, 1):
        print(f"{i}. {window['title']} (句柄: {window['hwnd']})")
    
    try:
        choice = int(input("\n请输入选择的序号: ")) - 1
        if 0 <= choice < len(mumu_windows):
            main_window = mumu_windows[choice]
            print(f"\n已选择主窗口: {main_window['title']}")
            print(f"主窗口句柄: {main_window['hwnd']}")
            
            # 查找子窗口
            print("\n查找子窗口...")
            child_windows = find_child_windows(main_window['hwnd'])
            
            if child_windows:
                print(f"找到 {len(child_windows)} 个子窗口:")
                print("-" * 40)
                for i, child in enumerate(child_windows, 1):
                    print(f"{i}. 句柄: {child['hwnd']}")
                    print(f"   标题: {child['title']}")
                    print(f"   类名: {child['class']}")
                    
                    # 查找孙子窗口
                    grandchild_windows = find_child_windows(child['hwnd'])
                    if grandchild_windows:
                        print(f"   子窗口 ({len(grandchild_windows)}个):")
                        for j, grandchild in enumerate(grandchild_windows, 1):
                            print(f"     {j}. 句柄: {grandchild['hwnd']}, 类名: {grandchild['class']}")
                    print()
            else:
                print("未找到子窗口")
            
            # 生成配置建议
            print("\n配置建议:")
            print("=" * 40)
            print(f"主窗口句柄: {main_window['hwnd']}")
            
            if child_windows:
                # 寻找可能的游戏窗口
                game_window = None
                for child in child_windows:
                    if 'nemuwin' in child['class'].lower():
                        game_window = child
                        break
                
                if not game_window and child_windows:
                    # 如果没找到nemuwin类，选择第一个子窗口
                    game_window = child_windows[0]
                
                if game_window:
                    print(f"子窗口句柄: {game_window['hwnd']}")
                    
                    # 查找游戏操作窗口
                    grandchildren = find_child_windows(game_window['hwnd'])
                    if grandchildren:
                        for grandchild in grandchildren:
                            if 'nemuwin' in grandchild['class'].lower():
                                print(f"游戏操作窗口句柄: {grandchild['hwnd']}")
                                break
                        else:
                            if grandchildren:
                                print(f"游戏操作窗口句柄: {grandchildren[0]['hwnd']}")
            
            print("\n请将这些句柄值更新到 game_automation.py 文件中的相应变量。")
            
        else:
            print("无效的选择")
            
    except ValueError:
        print("请输入有效的数字")
    except KeyboardInterrupt:
        print("\n程序被用户中断")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
