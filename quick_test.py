"""
快速测试修复后的图像识别功能
"""
import cv2
import numpy as np
from PIL import Image
import os

def load_image_with_chinese_path(filepath):
    """加载包含中文路径的图片文件"""
    try:
        # 使用numpy和cv2.imdecode处理中文路径
        with open(filepath, 'rb') as f:
            file_bytes = f.read()
        
        nparr = np.frombuffer(file_bytes, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        return img
        
    except Exception as e:
        try:
            # 使用PIL加载然后转换为OpenCV格式
            pil_img = Image.open(filepath)
            if pil_img.mode != 'RGB':
                pil_img = pil_img.convert('RGB')
            
            img_array = np.array(pil_img)
            img = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            
            return img
            
        except Exception as e2:
            return None

def test_image_recognition():
    """测试图像识别功能"""
    print("🔍 测试图像识别功能...")
    
    # 测试各个文件夹的图片识别
    folders = {
        "主页面": "Main page",
        "选择地图": "Map page", 
        "选择难度": "Level difficulty page",
        "选择角色": "Select a role",
        "战斗页面": "Game page",
        "结算页面": "Checkout page"
    }
    
    total_images = 0
    loaded_images = 0
    
    for folder_name, folder_path in folders.items():
        print(f"\n📁 测试 {folder_name} 文件夹:")
        
        if not os.path.exists(folder_path):
            print(f"  ✗ 文件夹不存在: {folder_path}")
            continue
            
        files = [f for f in os.listdir(folder_path) if f.lower().endswith(('.bmp', '.png', '.jpg', '.jpeg'))]
        
        if not files:
            print(f"  ✗ 文件夹为空: {folder_path}")
            continue
            
        for filename in files:
            filepath = os.path.join(folder_path, filename)
            total_images += 1
            
            try:
                template = load_image_with_chinese_path(filepath)
                if template is not None:
                    loaded_images += 1
                    print(f"  ✓ {filename}: 加载成功 - 尺寸: {template.shape}")
                else:
                    print(f"  ✗ {filename}: 加载失败")
            except Exception as e:
                print(f"  ! {filename}: 处理失败 - {str(e)}")
    
    # 测试敌兵识别
    print(f"\n🎯 测试敌兵识别:")
    enemy_folder = "Game page/enemy"
    if os.path.exists(enemy_folder):
        enemy_files = [f for f in os.listdir(enemy_folder) if f.lower().endswith(('.bmp', '.png', '.jpg', '.jpeg'))]
        
        enemy_loaded = 0
        for filename in enemy_files:
            filepath = os.path.join(enemy_folder, filename)
            total_images += 1
            
            try:
                enemy_template = load_image_with_chinese_path(filepath)
                if enemy_template is not None:
                    enemy_loaded += 1
                    loaded_images += 1
                    print(f"  ✓ {filename}: 加载成功 - 尺寸: {enemy_template.shape}")
                else:
                    print(f"  ✗ {filename}: 加载失败")
            except Exception as e:
                print(f"  ! {filename}: 处理失败 - {str(e)}")
        
        print(f"敌兵图片加载结果: {enemy_loaded}/{len(enemy_files)} 个敌兵图片加载成功")
    else:
        print("  ✗ 敌兵文件夹不存在")
    
    # 测试总结
    print(f"\n=== 🎉 测试总结 ===")
    print(f"总图片数: {total_images}")
    print(f"加载成功: {loaded_images}")
    print(f"加载率: {loaded_images/total_images*100:.1f}%" if total_images > 0 else "加载率: 0%")
    
    if loaded_images > 0:
        print("✅ 图像加载功能修复成功！")
        print("💡 现在可以正常使用自动化功能了")
    else:
        print("❌ 图像加载仍有问题，需要进一步检查")

if __name__ == "__main__":
    test_image_recognition()
