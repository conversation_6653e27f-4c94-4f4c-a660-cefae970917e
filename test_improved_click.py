"""
测试改进后的点击方法
"""
import win32gui
import win32api
import win32con
import time

def improved_click(hwnd, x, y):
    """改进的点击方法"""
    print(f"测试改进的点击方法: 窗口{hwnd}, 坐标({x}, {y})")
    
    # 验证坐标是否在有效范围内
    client_rect = win32gui.GetClientRect(hwnd)
    max_x, max_y = client_rect[2], client_rect[3]
    
    if x < 0 or y < 0 or x >= max_x or y >= max_y:
        print(f"警告: 坐标({x}, {y})超出游戏窗口范围(0,0,{max_x},{max_y})")
        x = max(0, min(x, max_x - 1))
        y = max(0, min(y, max_y - 1))
        print(f"坐标已调整为: ({x}, {y})")
    
    success = False
    
    # 方法1: 直接使用SendMessage（不设置焦点）
    try:
        print("尝试方法1: SendMessage")
        lParam = win32api.MAKELONG(x, y)
        
        # 发送鼠标移动消息
        win32api.SendMessage(hwnd, win32con.WM_MOUSEMOVE, 0, lParam)
        time.sleep(0.02)
        
        # 发送点击消息
        win32api.SendMessage(hwnd, win32con.WM_LBUTTONDOWN, win32con.MK_LBUTTON, lParam)
        time.sleep(0.05)
        win32api.SendMessage(hwnd, win32con.WM_LBUTTONUP, 0, lParam)
        success = True
        print(f"✓ SendMessage成功")
        
    except Exception as e1:
        print(f"✗ SendMessage失败: {str(e1)}")
        
        # 方法2: 使用PostMessage作为备用
        try:
            print("尝试方法2: PostMessage")
            lParam = win32api.MAKELONG(x, y)
            
            # 发送鼠标移动消息
            win32api.PostMessage(hwnd, win32con.WM_MOUSEMOVE, 0, lParam)
            time.sleep(0.02)
            
            # 发送点击消息
            win32api.PostMessage(hwnd, win32con.WM_LBUTTONDOWN, win32con.MK_LBUTTON, lParam)
            time.sleep(0.05)
            win32api.PostMessage(hwnd, win32con.WM_LBUTTONUP, 0, lParam)
            success = True
            print(f"✓ PostMessage成功")
            
        except Exception as e2:
            print(f"✗ PostMessage失败: {str(e2)}")
            
            # 方法3: 尝试向父窗口发送消息
            try:
                print("尝试方法3: 父窗口")
                parent_hwnd = win32gui.GetParent(hwnd)
                if parent_hwnd:
                    # 转换为父窗口坐标系
                    parent_rect = win32gui.GetWindowRect(parent_hwnd)
                    game_rect = win32gui.GetWindowRect(hwnd)
                    
                    # 计算相对于父窗口的坐标
                    parent_x = x + (game_rect[0] - parent_rect[0])
                    parent_y = y + (game_rect[1] - parent_rect[1])
                    
                    print(f"父窗口坐标: ({parent_x}, {parent_y})")
                    
                    parent_lParam = win32api.MAKELONG(parent_x, parent_y)
                    
                    win32api.SendMessage(parent_hwnd, win32con.WM_LBUTTONDOWN, win32con.MK_LBUTTON, parent_lParam)
                    time.sleep(0.05)
                    win32api.SendMessage(parent_hwnd, win32con.WM_LBUTTONUP, 0, parent_lParam)
                    success = True
                    print(f"✓ 父窗口点击成功")
                else:
                    print("✗ 没有父窗口")
                    
            except Exception as e3:
                print(f"✗ 父窗口点击失败: {str(e3)}")
    
    return success

def test_click_methods():
    """测试不同的点击方法"""
    print("=== 测试改进后的点击方法 ===")
    
    # MuMu窗口句柄
    game_hwnd = 918834
    
    # 测试坐标（从之前的调试结果）
    test_x, test_y = 118, 383
    
    print(f"目标窗口: {game_hwnd}")
    print(f"测试坐标: ({test_x}, {test_y})")
    print("请确保MuMu模拟器窗口可见...")
    print("5秒后开始测试...")
    time.sleep(5)
    
    # 执行改进的点击
    success = improved_click(game_hwnd, test_x, test_y)
    
    if success:
        print("🎉 点击执行成功！")
        print("请检查MuMu模拟器是否有反应")
    else:
        print("❌ 所有点击方法都失败了")
    
    print("\n=== 测试其他坐标 ===")
    # 测试屏幕中心点击
    center_x, center_y = 480, 270
    print(f"测试中心点击: ({center_x}, {center_y})")
    time.sleep(2)
    
    success2 = improved_click(game_hwnd, center_x, center_y)
    
    if success2:
        print("🎉 中心点击成功！")
    else:
        print("❌ 中心点击失败")

if __name__ == "__main__":
    test_click_methods()
