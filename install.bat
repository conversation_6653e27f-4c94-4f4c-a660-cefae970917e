@echo off
chcp 65001 >nul
echo ========================================
echo    游戏自动化助手 - 安装脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 正在安装Python依赖包...
echo ----------------------------------------
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo 警告: 部分依赖包安装失败
    echo 请手动运行以下命令安装:
    echo pip install opencv-python numpy Pillow pywin32 keyboard pytesseract
    echo.
) else (
    echo.
    echo 依赖包安装完成
    echo.
)

echo 正在检查ADB工具...
adb version >nul 2>&1
if errorlevel 1 (
    echo 警告: 未找到ADB工具
    echo ADB工具用于屏幕滑动操作，建议安装
    echo 下载地址: https://developer.android.com/studio/releases/platform-tools
    echo 请将adb.exe添加到系统PATH环境变量中
    echo.
) else (
    echo ADB工具检查通过
    echo.
)

echo 正在检查图片文件夹...
echo ----------------------------------------
set folders="Main page" "Map page" "Level difficulty page" "Select a role" "Game page" "Checkout page" "Game page\enemy"

for %%f in (%folders%) do (
    if exist %%f (
        echo ✓ 找到文件夹: %%f
    ) else (
        echo ✗ 缺少文件夹: %%f
        mkdir %%f 2>nul
        echo   已创建文件夹: %%f
    )
)

echo.
echo 安装完成！
echo.
echo 使用说明:
echo 1. 确保MuMu模拟器正在运行
echo 2. 运行 find_window_handles.py 查找正确的窗口句柄
echo 3. 运行 test_image_recognition.py 测试图像识别
echo 4. 运行 game_automation.py 启动自动化程序
echo.
echo 或者直接运行 start.bat 启动程序
echo.
pause
