"""
游戏自动化助手 - 功能演示脚本
展示新版本的主要特性和使用方法
"""

import tkinter as tk
from tkinter import ttk, messagebox
import time

class DemoApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("游戏自动化助手 - 功能演示")
        self.root.geometry("900x700")
        
        self.setup_ui()
        
    def setup_ui(self):
        # 标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill=tk.X, padx=20, pady=20)
        
        ttk.Label(title_frame, text="🎮 游戏自动化助手 - 集成版", 
                 font=("Arial", 20, "bold")).pack()
        ttk.Label(title_frame, text="专为MuMu模拟器设计的智能游戏助手", 
                 font=("Arial", 12)).pack(pady=5)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        self.create_features_tab()
        self.create_usage_tab()
        self.create_demo_tab()
        
    def create_features_tab(self):
        """功能特性选项卡"""
        features_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(features_frame, text="🌟 功能特性")
        
        # 主要特性
        main_features = ttk.LabelFrame(features_frame, text="主要功能", padding="15")
        main_features.pack(fill=tk.X, pady=10)
        
        features_text = """
✓ 后台自动化操作 - 不占用鼠标键盘
✓ 智能图像识别 - 基于OpenCV技术
✓ 六大页面流程 - 完整游戏自动化
✓ 热键快速控制 - 一键启动停止
✓ 实时统计监控 - 进度可视化
        """
        ttk.Label(main_features, text=features_text, font=("Arial", 11)).pack(anchor=tk.W)
        
        # 新版本特性
        new_features = ttk.LabelFrame(features_frame, text="🆕 新版本特性", padding="15")
        new_features.pack(fill=tk.X, pady=10)
        
        new_text = """
🔧 集成化界面 - 三大功能模块统一管理
🪟 窗口设置工具 - 自动扫描和配置MuMu窗口
🔍 图像测试工具 - 实时验证识别准确率
📊 可视化调试 - 详细的测试报告和日志
⚙️ 智能配置 - 自动保存和加载设置
        """
        ttk.Label(new_features, text=new_text, font=("Arial", 11)).pack(anchor=tk.W)
        
        # 技术优势
        tech_features = ttk.LabelFrame(features_frame, text="技术优势", padding="15")
        tech_features.pack(fill=tk.X, pady=10)
        
        tech_text = """
🎯 高精度识别 - 多阈值图像匹配算法
⚡ 高效执行 - 优化的操作流程和延迟控制
🛡️ 稳定可靠 - 完善的错误处理和异常恢复
🔄 智能循环 - 自动检测页面状态和流程跳转
📝 详细日志 - 完整的操作记录和调试信息
        """
        ttk.Label(tech_features, text=tech_text, font=("Arial", 11)).pack(anchor=tk.W)
        
    def create_usage_tab(self):
        """使用指南选项卡"""
        usage_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(usage_frame, text="📖 使用指南")
        
        # 快速开始
        quick_start = ttk.LabelFrame(usage_frame, text="🚀 快速开始", padding="15")
        quick_start.pack(fill=tk.X, pady=10)
        
        steps_text = """
1️⃣ 启动MuMu模拟器并打开游戏
2️⃣ 运行 game_automation.py 启动程序
3️⃣ 在"窗口设置"选项卡配置窗口句柄
4️⃣ 在"图像测试"选项卡验证功能
5️⃣ 在"主要功能"选项卡开始自动化
        """
        ttk.Label(quick_start, text=steps_text, font=("Arial", 11)).pack(anchor=tk.W)
        
        # 详细步骤
        detailed_steps = ttk.LabelFrame(usage_frame, text="详细操作步骤", padding="15")
        detailed_steps.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建滚动文本框
        text_frame = ttk.Frame(detailed_steps)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Arial", 10))
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        detailed_text = """
🪟 窗口设置选项卡：
• 点击"扫描窗口"自动搜索MuMu窗口
• 双击窗口列表中的项目进行选择
• 选择窗口类型：主窗口/子窗口/游戏窗口
• 点击"应用设置"保存配置
• 使用"测试连接"验证设置

🔍 图像测试选项卡：
• "捕获游戏窗口" - 获取当前游戏画面
• "测试图像识别" - 检查所有图片匹配情况
• "保存调试截图" - 保存重要区域截图
• 查看测试结果和匹配率报告

🎮 主要功能选项卡：
• 设置启动热键（默认F1）
• 点击"启动"开始自动化
• 监控运行日志和统计信息
• 随时可以停止或重新启动

⚠️ 注意事项：
• 确保MuMu模拟器窗口可见且未最小化
• 游戏分辨率应与图片文件匹配
• 首次使用建议先测试各项功能
• 定期检查图片文件的完整性
        """
        
        text_widget.insert(tk.END, detailed_text)
        text_widget.config(state=tk.DISABLED)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_demo_tab(self):
        """演示选项卡"""
        demo_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(demo_frame, text="🎬 功能演示")
        
        # 演示控制
        control_frame = ttk.LabelFrame(demo_frame, text="演示控制", padding="15")
        control_frame.pack(fill=tk.X, pady=10)
        
        button_frame = ttk.Frame(control_frame)
        button_frame.pack()
        
        ttk.Button(button_frame, text="🪟 演示窗口设置", 
                  command=self.demo_window_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔍 演示图像测试", 
                  command=self.demo_image_test).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🎮 演示自动化流程", 
                  command=self.demo_automation).pack(side=tk.LEFT, padx=5)
        
        # 演示结果
        result_frame = ttk.LabelFrame(demo_frame, text="演示结果", padding="15")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.demo_text = tk.Text(result_frame, wrap=tk.WORD, font=("Consolas", 10))
        demo_scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.demo_text.yview)
        self.demo_text.configure(yscrollcommand=demo_scrollbar.set)
        
        self.demo_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        demo_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 初始化演示文本
        self.demo_log("🎬 欢迎使用游戏自动化助手演示程序！")
        self.demo_log("点击上方按钮体验各项功能的演示")
        
    def demo_log(self, message):
        """演示日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.demo_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.demo_text.see(tk.END)
        self.root.update()
        
    def demo_window_settings(self):
        """演示窗口设置功能"""
        self.demo_log("🪟 开始演示窗口设置功能...")
        time.sleep(0.5)
        
        self.demo_log("1. 扫描系统中的所有窗口...")
        time.sleep(1)
        self.demo_log("   找到 15 个可见窗口")
        
        self.demo_log("2. 筛选MuMu相关窗口...")
        time.sleep(1)
        self.demo_log("   ✓ 主窗口: 句柄 854566, 类名 Qt5156QWindowIcon")
        self.demo_log("   ✓ 子窗口: 句柄 66862, 类名 Qt5156QWindowIcon")
        self.demo_log("   ✓ 游戏窗口: 句柄 918834, 类名 nemuwin")
        
        self.demo_log("3. 测试窗口连接...")
        time.sleep(1)
        self.demo_log("   ✓ 主窗口连接成功")
        self.demo_log("   ✓ 子窗口连接成功")
        self.demo_log("   ✓ 游戏窗口连接成功")
        
        self.demo_log("🎉 窗口设置演示完成！")
        
    def demo_image_test(self):
        """演示图像测试功能"""
        self.demo_log("🔍 开始演示图像测试功能...")
        time.sleep(0.5)
        
        self.demo_log("1. 捕获游戏窗口截图...")
        time.sleep(1)
        self.demo_log("   ✓ 截图成功，尺寸: (720, 1280, 3)")
        
        self.demo_log("2. 测试主页面图片识别...")
        time.sleep(1)
        self.demo_log("   ✓ 主页面.bmp: 匹配成功 - 位置(640, 360), 相似度: 0.892")
        self.demo_log("   ✓ 选择地图.bmp: 匹配成功 - 位置(640, 500), 相似度: 0.856")
        
        self.demo_log("3. 测试敌兵识别...")
        time.sleep(1)
        self.demo_log("   ✓ 刀兵.bmp: 在技能区域发现敌兵 - 位置(750, 450), 相似度: 0.823")
        self.demo_log("   ✗ 弓兵.bmp: 未发现敌兵")
        
        self.demo_log("4. 生成测试报告...")
        time.sleep(1)
        self.demo_log("   总图片数: 45")
        self.demo_log("   匹配成功: 38")
        self.demo_log("   匹配率: 84.4%")
        
        self.demo_log("🎉 图像测试演示完成！")
        
    def demo_automation(self):
        """演示自动化流程"""
        self.demo_log("🎮 开始演示自动化流程...")
        time.sleep(0.5)
        
        pages = [
            ("主页面", "点击选择地图按钮"),
            ("选择地图页面", "点击选择关卡按钮"),
            ("选择难度页面", "切换到困难难度并进入战场"),
            ("选择角色页面", "点击开始战斗按钮"),
            ("战斗页面", "执行复杂战斗策略"),
            ("结算页面", "点击继续按钮返回主页面")
        ]
        
        for i, (page, action) in enumerate(pages, 1):
            self.demo_log(f"{i}. 识别到{page}")
            time.sleep(1)
            self.demo_log(f"   执行操作: {action}")
            time.sleep(1.5)
            
        self.demo_log("🔄 完成一轮完整流程，准备下一轮...")
        self.demo_log("📊 统计信息: 进入主页面次数 +1")
        self.demo_log("🎉 自动化流程演示完成！")
        
    def run(self):
        """运行演示程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = DemoApp()
    app.run()
