@echo off
chcp 65001 >nul
echo ========================================
echo    游戏自动化助手 - 集成版
echo ========================================
echo.
echo 新版本特性：
echo ✓ 集成化界面设计
echo ✓ 窗口设置选项卡
echo ✓ 图像测试选项卡
echo ✓ 实时调试功能
echo.
echo 使用前请确保：
echo 1. MuMu模拟器已启动
echo 2. 游戏已打开到主界面
echo 3. 所有图片文件已准备就绪
echo.
echo 首次使用建议：
echo 1. 先使用"窗口设置"选项卡配置窗口句柄
echo 2. 使用"图像测试"选项卡验证识别功能
echo 3. 最后在"主要功能"选项卡启动自动化
echo.
pause
echo 正在启动程序...
python game_automation.py
if errorlevel 1 (
    echo.
    echo 程序启动失败，请检查：
    echo 1. Python环境是否正确安装
    echo 2. 依赖包是否已安装 (pip install -r requirements.txt)
    echo 3. 是否有权限访问窗口
)
pause
