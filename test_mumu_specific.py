"""
测试MuMu模拟器特定的点击方法
"""
import win32gui
import win32api
import win32con
import win32ui
import cv2
import numpy as np
from ctypes import windll
import time

def capture_window(hwnd):
    """捕获指定窗口的截图"""
    try:
        rect = win32gui.GetWindowRect(hwnd)
        width = rect[2] - rect[0]
        height = rect[3] - rect[1]

        hwndDC = win32gui.GetWindowDC(hwnd)
        mfcDC = win32ui.CreateDCFromHandle(hwndDC)
        saveDC = mfcDC.CreateCompatibleDC()

        saveBitMap = win32ui.CreateBitmap()
        saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
        saveDC.SelectObject(saveBitMap)

        result = windll.user32.PrintWindow(hwnd, saveDC.GetSafeHdc(), 3)

        if result:
            bmpstr = saveBitMap.GetBitmapBits(True)
            img = np.frombuffer(bmpstr, dtype='uint8')
            img.shape = (height, width, 4)
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
            
            # 清理资源
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(hwnd, hwndDC)
            
            return img
        else:
            return None
            
    except Exception as e:
        print(f"捕获窗口失败: {e}")
        return None

def mumu_click(hwnd, x, y):
    """MuMu模拟器专用点击方法"""
    print(f"MuMu专用点击: 窗口{hwnd}, 坐标({x}, {y})")
    
    try:
        # 验证坐标
        client_rect = win32gui.GetClientRect(hwnd)
        max_x, max_y = client_rect[2], client_rect[3]
        
        if x < 0 or y < 0 or x >= max_x or y >= max_y:
            print(f"坐标超出范围，调整中...")
            x = max(0, min(x, max_x - 1))
            y = max(0, min(y, max_y - 1))
        
        lParam = win32api.MAKELONG(x, y)
        
        # MuMu特定的消息序列
        print("发送消息序列...")
        
        # 1. 鼠标移动到目标位置
        win32api.SendMessage(hwnd, win32con.WM_MOUSEMOVE, 0, lParam)
        time.sleep(0.05)
        
        # 2. 鼠标按下
        win32api.SendMessage(hwnd, win32con.WM_LBUTTONDOWN, win32con.MK_LBUTTON, lParam)
        time.sleep(0.1)  # 稍长的延迟
        
        # 3. 鼠标释放
        win32api.SendMessage(hwnd, win32con.WM_LBUTTONUP, 0, lParam)
        time.sleep(0.05)
        
        print("✓ 消息发送完成")
        return True
        
    except Exception as e:
        print(f"✗ 点击失败: {e}")
        return False

def test_mumu_click_with_verification():
    """测试MuMu点击并验证效果"""
    print("=== MuMu模拟器点击测试 ===")
    
    game_hwnd = 918834
    test_x, test_y = 118, 383  # 选择地图按钮位置
    
    print("1. 捕获点击前的截图...")
    before_screenshot = capture_window(game_hwnd)
    if before_screenshot is not None:
        cv2.imwrite("before_click.png", before_screenshot)
        print("✓ 点击前截图保存为: before_click.png")
    
    print("2. 执行点击...")
    print("5秒后开始点击测试...")
    time.sleep(5)
    
    success = mumu_click(game_hwnd, test_x, test_y)
    
    if success:
        print("3. 等待游戏响应...")
        time.sleep(3)  # 等待游戏响应
        
        print("4. 捕获点击后的截图...")
        after_screenshot = capture_window(game_hwnd)
        if after_screenshot is not None:
            cv2.imwrite("after_click.png", after_screenshot)
            print("✓ 点击后截图保存为: after_click.png")
            
            # 比较前后截图
            if before_screenshot is not None:
                diff = cv2.absdiff(before_screenshot, after_screenshot)
                diff_sum = np.sum(diff)
                print(f"截图差异值: {diff_sum}")
                
                # 保存差异图
                cv2.imwrite("click_diff.png", diff)
                print("✓ 差异图保存为: click_diff.png")
                
                if diff_sum > 1000000:  # 阈值可能需要调整
                    print("🎉 检测到明显的画面变化！点击生效了！")
                    return True
                else:
                    print("⚠️ 画面变化很小，点击可能未生效")
                    
                    # 检查特定区域的变化
                    # 选择地图按钮区域
                    button_region_before = before_screenshot[365:400, 100:140]
                    button_region_after = after_screenshot[365:400, 100:140]
                    button_diff = cv2.absdiff(button_region_before, button_region_after)
                    button_diff_sum = np.sum(button_diff)
                    
                    print(f"按钮区域差异值: {button_diff_sum}")
                    
                    if button_diff_sum > 10000:
                        print("🎉 按钮区域有变化！点击可能生效了！")
                        return True
                    else:
                        print("❌ 按钮区域无变化，点击未生效")
                        return False
        else:
            print("✗ 无法捕获点击后截图")
    else:
        print("✗ 点击执行失败")
    
    return False

def test_alternative_methods():
    """测试其他可能的方法"""
    print("\n=== 测试其他方法 ===")
    
    game_hwnd = 918834
    test_x, test_y = 118, 383
    
    # 方法1: 使用WM_COMMAND
    print("1. 尝试WM_COMMAND...")
    try:
        win32api.SendMessage(game_hwnd, win32con.WM_COMMAND, 0, 0)
        print("✓ WM_COMMAND发送成功")
    except Exception as e:
        print(f"✗ WM_COMMAND失败: {e}")
    
    time.sleep(1)
    
    # 方法2: 多次点击
    print("2. 尝试多次点击...")
    for i in range(3):
        print(f"  第{i+1}次点击...")
        mumu_click(game_hwnd, test_x, test_y)
        time.sleep(0.5)
    
    # 方法3: 不同的延迟
    print("3. 尝试更长延迟...")
    try:
        lParam = win32api.MAKELONG(test_x, test_y)
        
        win32api.SendMessage(game_hwnd, win32con.WM_MOUSEMOVE, 0, lParam)
        time.sleep(0.2)
        
        win32api.SendMessage(game_hwnd, win32con.WM_LBUTTONDOWN, win32con.MK_LBUTTON, lParam)
        time.sleep(0.3)  # 更长的按下时间
        
        win32api.SendMessage(game_hwnd, win32con.WM_LBUTTONUP, 0, lParam)
        
        print("✓ 长延迟点击完成")
    except Exception as e:
        print(f"✗ 长延迟点击失败: {e}")

if __name__ == "__main__":
    # 主测试
    result = test_mumu_click_with_verification()
    
    if not result:
        # 如果主测试失败，尝试其他方法
        test_alternative_methods()
    
    print("\n测试完成！请检查生成的截图文件来验证效果。")
