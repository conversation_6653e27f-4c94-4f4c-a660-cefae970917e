"""
调试图像识别坐标问题
"""
import cv2
import numpy as np
import win32gui
import win32ui
import win32api
import win32con
from ctypes import windll
import time

def load_image_with_chinese_path(filepath):
    """加载包含中文路径的图片文件"""
    try:
        with open(filepath, 'rb') as f:
            file_bytes = f.read()
        nparr = np.frombuffer(file_bytes, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        return img
    except Exception:
        return None

def capture_window(hwnd):
    """捕获指定窗口的截图"""
    try:
        rect = win32gui.GetWindowRect(hwnd)
        width = rect[2] - rect[0]
        height = rect[3] - rect[1]

        hwndDC = win32gui.GetWindowDC(hwnd)
        mfcDC = win32ui.CreateDCFromHandle(hwndDC)
        saveDC = mfcDC.CreateCompatibleDC()

        saveBitMap = win32ui.CreateBitmap()
        saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
        saveDC.SelectObject(saveBitMap)

        result = windll.user32.PrintWindow(hwnd, saveDC.GetSafeHdc(), 3)

        if result:
            bmpstr = saveBitMap.GetBitmapBits(True)
            img = np.frombuffer(bmpstr, dtype='uint8')
            img.shape = (height, width, 4)
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
            
            # 清理资源
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(hwnd, hwndDC)
            
            return img
        else:
            return None
            
    except Exception as e:
        print(f"捕获窗口失败: {e}")
        return None

def find_image_in_screenshot(screenshot, template, threshold=0.8):
    """在截图中查找模板图片"""
    try:
        if screenshot is None or template is None:
            return None

        result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
        _, max_val, _, max_loc = cv2.minMaxLoc(result)

        if max_val >= threshold:
            h, w = template.shape[:2]
            center_x = max_loc[0] + w // 2
            center_y = max_loc[1] + h // 2
            return (center_x, center_y, max_val)
        else:
            return None

    except Exception as e:
        print(f"图像匹配失败: {e}")
        return None

def debug_image_recognition():
    """调试图像识别和坐标"""
    print("=== 调试图像识别坐标 ===")
    
    # MuMu窗口句柄
    game_hwnd = 918834
    
    # 捕获游戏窗口
    print("1. 捕获游戏窗口...")
    screenshot = capture_window(game_hwnd)
    
    if screenshot is None:
        print("✗ 无法捕获游戏窗口")
        return
    
    print(f"✓ 游戏窗口捕获成功，尺寸: {screenshot.shape}")
    cv2.imwrite("debug_current_screenshot.png", screenshot)
    print("当前截图已保存为: debug_current_screenshot.png")
    
    # 获取窗口信息
    client_rect = win32gui.GetClientRect(game_hwnd)
    window_rect = win32gui.GetWindowRect(game_hwnd)
    print(f"窗口矩形: {window_rect}")
    print(f"客户区矩形: {client_rect}")
    
    # 测试主页面图片识别
    print("\n2. 测试主页面图片识别...")
    
    # 加载主页面图片
    main_page_img = load_image_with_chinese_path("Main page/主页面.bmp")
    select_map_img = load_image_with_chinese_path("Main page/选择地图.bmp")
    
    if main_page_img is not None:
        print(f"✓ 主页面.bmp 加载成功，尺寸: {main_page_img.shape}")
        
        # 查找主页面
        match = find_image_in_screenshot(screenshot, main_page_img, threshold=0.7)
        if match:
            print(f"✓ 找到主页面，位置: ({match[0]}, {match[1]}), 相似度: {match[2]:.3f}")
        else:
            print("✗ 未找到主页面")
    
    if select_map_img is not None:
        print(f"✓ 选择地图.bmp 加载成功，尺寸: {select_map_img.shape}")
        
        # 查找选择地图按钮
        match = find_image_in_screenshot(screenshot, select_map_img, threshold=0.7)
        if match:
            x, y, confidence = match
            print(f"✓ 找到选择地图按钮，位置: ({x}, {y}), 相似度: {confidence:.3f}")
            
            # 检查坐标是否在有效范围内
            max_x, max_y = client_rect[2], client_rect[3]
            print(f"游戏窗口大小: {max_x} x {max_y}")
            
            if 0 <= x < max_x and 0 <= y < max_y:
                print(f"✓ 坐标在有效范围内")
                
                # 在截图上标记找到的位置
                marked_screenshot = screenshot.copy()
                cv2.circle(marked_screenshot, (x, y), 10, (0, 255, 0), 2)
                cv2.putText(marked_screenshot, f"({x},{y})", (x+15, y-15), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                cv2.imwrite("debug_marked_screenshot.png", marked_screenshot)
                print("标记后的截图已保存为: debug_marked_screenshot.png")
                
                # 测试点击
                print(f"\n3. 测试点击坐标 ({x}, {y})...")
                print("5秒后执行点击测试...")
                time.sleep(5)
                
                try:
                    # 尝试激活父窗口
                    parent_hwnd = win32gui.GetParent(game_hwnd)
                    if parent_hwnd:
                        win32gui.SetForegroundWindow(parent_hwnd)
                        time.sleep(0.1)
                    
                    lParam = win32api.MAKELONG(x, y)
                    win32api.SendMessage(game_hwnd, win32con.WM_LBUTTONDOWN, win32con.MK_LBUTTON, lParam)
                    time.sleep(0.05)
                    win32api.SendMessage(game_hwnd, win32con.WM_LBUTTONUP, 0, lParam)
                    print(f"✓ 点击执行完成")
                    
                    # 等待一下再截图看效果
                    time.sleep(2)
                    after_screenshot = capture_window(game_hwnd)
                    if after_screenshot is not None:
                        cv2.imwrite("debug_after_click.png", after_screenshot)
                        print("点击后的截图已保存为: debug_after_click.png")
                        
                        # 比较前后截图
                        diff = cv2.absdiff(screenshot, after_screenshot)
                        diff_sum = np.sum(diff)
                        print(f"截图差异值: {diff_sum}")
                        
                        if diff_sum > 1000000:  # 如果有明显变化
                            print("✓ 检测到画面变化，点击可能生效了")
                        else:
                            print("✗ 画面无明显变化，点击可能未生效")
                    
                except Exception as e:
                    print(f"✗ 点击测试失败: {e}")
                
            else:
                print(f"✗ 坐标超出范围！有效范围: (0,0) 到 ({max_x-1},{max_y-1})")
        else:
            print("✗ 未找到选择地图按钮")

if __name__ == "__main__":
    debug_image_recognition()
