import os
import cv2

def test_image_loading():
    """测试图片加载功能"""
    print("测试图片加载功能...")
    
    # 定义图片文件夹
    folders = {
        "主页面": "Main page",
        "选择地图": "Map page", 
        "选择难度": "Level difficulty page",
        "选择角色": "Select a role",
        "战斗页面": "Game page",
        "结算页面": "Checkout page"
    }
    
    total_images = 0
    loaded_images = 0
    
    for folder_name, folder_path in folders.items():
        print(f"\n检查文件夹: {folder_name} ({folder_path})")
        
        if not os.path.exists(folder_path):
            print(f"  ✗ 文件夹不存在: {folder_path}")
            continue
            
        files = [f for f in os.listdir(folder_path) if f.lower().endswith(('.bmp', '.png', '.jpg', '.jpeg'))]
        
        if not files:
            print(f"  ✗ 文件夹为空: {folder_path}")
            continue
            
        print(f"  找到 {len(files)} 个图片文件:")
        
        for filename in files:
            filepath = os.path.join(folder_path, filename)
            total_images += 1
            
            try:
                # 测试文件是否存在
                if not os.path.exists(filepath):
                    print(f"    ✗ {filename}: 文件不存在")
                    continue
                
                # 测试OpenCV是否能加载
                img = cv2.imread(filepath)
                if img is not None:
                    loaded_images += 1
                    print(f"    ✓ {filename}: 加载成功 - 尺寸: {img.shape}")
                else:
                    print(f"    ✗ {filename}: OpenCV无法加载")
                    
                    # 尝试用其他方法检查文件
                    file_size = os.path.getsize(filepath)
                    print(f"      文件大小: {file_size} 字节")
                    
            except Exception as e:
                print(f"    ! {filename}: 处理失败 - {str(e)}")
    
    print(f"\n=== 测试总结 ===")
    print(f"总图片数: {total_images}")
    print(f"加载成功: {loaded_images}")
    print(f"加载率: {loaded_images/total_images*100:.1f}%" if total_images > 0 else "加载率: 0%")
    
    # 测试敌兵文件夹
    print(f"\n检查敌兵文件夹:")
    enemy_folder = "Game page/enemy"
    if os.path.exists(enemy_folder):
        enemy_files = [f for f in os.listdir(enemy_folder) if f.lower().endswith(('.bmp', '.png', '.jpg', '.jpeg'))]
        print(f"  找到 {len(enemy_files)} 个敌兵图片:")
        
        enemy_loaded = 0
        for filename in enemy_files:
            filepath = os.path.join(enemy_folder, filename)
            try:
                img = cv2.imread(filepath)
                if img is not None:
                    enemy_loaded += 1
                    print(f"    ✓ {filename}: 加载成功")
                else:
                    print(f"    ✗ {filename}: 加载失败")
            except Exception as e:
                print(f"    ! {filename}: 处理失败 - {str(e)}")
        
        print(f"  敌兵图片加载率: {enemy_loaded}/{len(enemy_files)}")
    else:
        print(f"  ✗ 敌兵文件夹不存在: {enemy_folder}")

if __name__ == "__main__":
    test_image_loading()
