import cv2
import numpy as np
from PIL import Image
import os

def load_image_with_chinese_path(filepath):
    """加载包含中文路径的图片文件"""
    try:
        # 方法1: 使用numpy和cv2.imdecode处理中文路径
        # 读取文件为字节流
        with open(filepath, 'rb') as f:
            file_bytes = f.read()
        
        # 转换为numpy数组
        nparr = np.frombuffer(file_bytes, np.uint8)
        
        # 使用cv2.imdecode解码图片
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        return img
        
    except Exception as e:
        try:
            # 方法2: 使用PIL加载然后转换为OpenCV格式
            pil_img = Image.open(filepath)
            # 转换为RGB（PIL默认是RGB，OpenCV是BGR）
            if pil_img.mode != 'RGB':
                pil_img = pil_img.convert('RGB')
            
            # 转换为numpy数组
            img_array = np.array(pil_img)
            
            # 转换BGR格式（OpenCV格式）
            img = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            
            return img
            
        except Exception as e2:
            # 如果都失败了，返回None
            print(f"两种方法都失败: {e}, {e2}")
            return None

def test_chinese_image_loading():
    """测试中文路径图片加载"""
    print("测试中文路径图片加载功能...")
    
    # 测试几个具体的文件
    test_files = [
        "Main page/主页面.bmp",
        "Main page/选择地图.bmp",
        "Game page/关平.bmp",
        "Game page/星彩.bmp"
    ]
    
    for filepath in test_files:
        print(f"\n测试文件: {filepath}")
        
        if not os.path.exists(filepath):
            print(f"  ✗ 文件不存在")
            continue
        
        # 测试原始cv2.imread
        img1 = cv2.imread(filepath)
        if img1 is not None:
            print(f"  ✓ cv2.imread成功: {img1.shape}")
        else:
            print(f"  ✗ cv2.imread失败")
        
        # 测试新的加载方法
        img2 = load_image_with_chinese_path(filepath)
        if img2 is not None:
            print(f"  ✓ 新方法成功: {img2.shape}")
            
            # 保存测试图片
            test_filename = f"test_{os.path.basename(filepath)}"
            cv2.imwrite(test_filename, img2)
            print(f"  ✓ 测试图片已保存: {test_filename}")
        else:
            print(f"  ✗ 新方法失败")

if __name__ == "__main__":
    test_chinese_image_loading()
