# 游戏自动化助手

这是一个专为MuMu模拟器设计的游戏自动化软件，可以自动执行游戏操作，类似于按键精灵的功能。

## 🆕 新版本特性

### 集成化界面
- **选项卡设计**: 将所有功能集成到一个主程序中
- **窗口设置**: 专门的窗口句柄配置选项卡
- **图像测试**: 内置图像识别测试工具
- **实时调试**: 可视化的测试结果和日志显示

### 三大功能模块

#### 1. 主要功能选项卡
- 🎮 启动/停止自动化
- ⌨️ 自定义热键设置
- 📊 进入主页面次数统计
- 📝 实时运行日志

#### 2. 窗口设置选项卡
- 🔍 自动扫描MuMu模拟器窗口
- ⚙️ 配置三个窗口句柄（主窗口、子窗口、游戏窗口）
- 🔗 测试窗口连接状态
- 📋 可视化窗口列表显示

#### 3. 图像测试选项卡
- 📸 捕获游戏窗口截图
- 🔍 测试图像识别准确率
- 💾 保存调试截图
- 📊 详细的测试结果报告

## 功能特点

- 🎮 **后台操作**: 所有操作都在后台执行，不占用电脑的鼠标键盘和屏幕
- 🖼️ **图像识别**: 基于OpenCV的图像识别技术，准确识别游戏界面
- ⌨️ **热键支持**: 支持自定义热键启动/停止自动化
- 📊 **统计功能**: 实时显示进入主页面的次数
- 🔄 **智能流程**: 自动处理六个主要游戏页面的操作流程
- 🛠️ **调试工具**: 内置窗口检测和图像测试工具

## 系统要求

- Windows 10/11
- Python 3.8+
- MuMu模拟器
- ADB工具（用于屏幕滑动操作）

## 安装步骤

1. **克隆或下载项目**
   ```bash
   git clone <项目地址>
   cd sg3
   ```

2. **安装Python依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **安装ADB工具**
   - 下载Android SDK Platform Tools
   - 将adb.exe添加到系统PATH环境变量中

4. **准备图片文件**
   确保以下文件夹存在并包含相应的图片文件：
   - `Main page/` - 主页面相关图片
   - `Map page/` - 选择地图页面相关图片
   - `Level difficulty page/` - 选择难度页面相关图片
   - `Select a role/` - 选择角色页面相关图片
   - `Game page/` - 战斗页面相关图片
   - `Game page/enemy/` - 敌兵图片
   - `Checkout page/` - 结算页面相关图片

## 使用方法

### 🚀 快速开始

1. **启动MuMu模拟器**
   确保MuMu模拟器正常运行，游戏已打开

2. **运行自动化程序**
   ```bash
   python game_automation.py
   ```

3. **首次配置**
   - 切换到"窗口设置"选项卡
   - 点击"扫描窗口"按钮
   - 双击选择正确的MuMu窗口
   - 点击"应用设置"保存配置

4. **测试功能**
   - 切换到"图像测试"选项卡
   - 点击"测试窗口连接"验证窗口设置
   - 点击"捕获游戏窗口"测试截图功能
   - 点击"测试图像识别"检查识别准确率

5. **开始自动化**
   - 切换到"主要功能"选项卡
   - 设置启动热键（默认F1）
   - 点击"启动"按钮或按热键开始自动化

### 📋 详细操作指南

#### 窗口设置选项卡使用方法

1. **扫描窗口**
   - 点击"扫描窗口"按钮自动搜索MuMu相关窗口
   - 在窗口列表中查看所有可用窗口

2. **选择窗口**
   - 双击窗口列表中的项目
   - 在弹出对话框中选择窗口类型：
     - "是" = 主窗口
     - "否" = 子窗口
     - "取消" = 游戏窗口

3. **手动设置**
   - 也可以直接在输入框中输入窗口句柄和类名
   - 点击"应用设置"保存配置

4. **测试连接**
   - 点击"测试连接"验证所有窗口是否可用
   - 查看测试结果确认配置正确

#### 图像测试选项卡使用方法

1. **捕获窗口**
   - 点击"捕获游戏窗口"获取当前游戏画面
   - 截图将保存为debug_game_window.png

2. **测试识别**
   - 点击"测试图像识别"检查所有图片的匹配情况
   - 查看详细的测试报告和匹配率

3. **保存调试截图**
   - 点击"保存调试截图"保存各个重要区域的截图
   - 用于调试和验证识别区域

4. **查看结果**
   - 在测试结果区域查看详细的日志信息
   - 根据结果调整图片或设置

## 配置说明

### MuMu模拟器窗口句柄
程序中预设的窗口句柄：
- 主窗口句柄: 854566
- 子窗口句柄: 66862  
- 游戏操作窗口句柄: 918834

如果您的MuMu模拟器句柄不同，请修改`game_automation.py`中的相应值。

### 图片匹配阈值
默认图片匹配阈值为0.7-0.8，如果识别不准确，可以调整代码中的threshold参数。

## 游戏流程

软件会自动处理以下六个主要页面：

1. **主页面** - 自动点击"选择地图"
2. **选择地图页面** - 自动点击"选择关卡"
3. **选择难度页面** - 自动选择困难难度并进入战场
4. **选择角色页面** - 自动点击"开始战斗"
5. **战斗页面** - 执行复杂的战斗策略
6. **结算页面** - 自动点击继续按钮返回主页面

## 战斗策略

战斗页面包含以下自动化操作：
- 初始屏幕拖动和角色放置
- 智能技能释放（基于敌兵检测）
- 角色升级和合并
- 实时监控包子数量
- 自动放置星彩和关平角色

## 注意事项

⚠️ **重要提醒**：
- 请确保在合法合规的范围内使用此软件
- 建议在测试环境中先试运行
- 定期备份游戏数据
- 如遇到问题，请查看日志信息

## 故障排除

### 常见问题

1. **无法捕获游戏窗口**
   - 检查MuMu模拟器是否正常运行
   - 确认窗口句柄是否正确
   - 尝试以管理员权限运行程序

2. **图像识别不准确**
   - 检查图片文件是否存在且格式正确
   - 调整匹配阈值参数
   - 确保游戏分辨率与图片匹配

3. **ADB命令失败**
   - 确认ADB工具已正确安装
   - 检查模拟器ADB连接状态
   - 尝试重启模拟器

## 技术支持

如果您在使用过程中遇到问题，请：
1. 查看运行日志中的错误信息
2. 检查配置文件是否正确
3. 确认所有依赖包已正确安装

## 更新日志

### v1.0.0
- 初始版本发布
- 支持六个主要游戏页面的自动化操作
- 实现图像识别和后台操作功能
- 添加热键支持和统计功能
