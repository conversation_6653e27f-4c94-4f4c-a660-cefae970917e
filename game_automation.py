import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import cv2
import numpy as np
import win32gui
import win32con
import win32api
import win32ui
from PIL import Image, ImageTk
import threading
import time
import os
import keyboard
import json
from ctypes import windll, byref, c_ubyte
from ctypes.wintypes import RECT, HWND
import subprocess

class GameAutomation:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("游戏自动化助手")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 配置变量
        self.is_running = False
        self.main_page_count = 0
        self.hotkey = "F1"  # 默认热键
        self.thread = None
        
        # MuMu模拟器窗口信息
        self.mumu_hwnd = 854566  # 主窗口句柄
        self.mumu_child_hwnd = 66862  # 子窗口句柄
        self.mumu_game_hwnd = 918834  # 游戏操作窗口句柄
        
        # 图片缓存
        self.images = {}
        
        # 当前游戏状态
        self.current_page = "unknown"
        self.last_skill_time = 0
        self.star_placed = False
        self.guan_ping_placed = False
        self.monitoring_baozi = True
        
        self.setup_ui()
        self.load_images()
        self.load_config()
        self.setup_hotkey()

        # 配置根窗口的网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
    def setup_ui(self):
        """设置用户界面"""
        # 创建选项卡控件
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建各个选项卡
        self.setup_main_tab()
        self.setup_window_settings_tab()
        self.setup_image_test_tab()

    def setup_main_tab(self):
        """设置主要功能选项卡"""
        # 主功能选项卡
        main_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(main_frame, text="主要功能")

        # 标题
        title_label = ttk.Label(main_frame, text="游戏自动化助手", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 启动/停止按钮
        self.start_button = ttk.Button(main_frame, text="启动", command=self.toggle_automation)
        self.start_button.grid(row=1, column=0, columnspan=2, pady=10, sticky=(tk.W, tk.E))

        # 热键设置
        hotkey_frame = ttk.Frame(main_frame)
        hotkey_frame.grid(row=2, column=0, columnspan=2, pady=10, sticky=(tk.W, tk.E))

        ttk.Label(hotkey_frame, text="启动热键:").grid(row=0, column=0, padx=(0, 10))
        self.hotkey_var = tk.StringVar(value=self.hotkey)
        self.hotkey_entry = ttk.Entry(hotkey_frame, textvariable=self.hotkey_var, width=10)
        self.hotkey_entry.grid(row=0, column=1, padx=(0, 10))

        ttk.Button(hotkey_frame, text="设置", command=self.set_hotkey).grid(row=0, column=2)

        # 进入主页面次数显示
        self.count_label = ttk.Label(main_frame, text=f"当前进入主页面的次数为 {self.main_page_count} 次")
        self.count_label.grid(row=3, column=0, columnspan=2, pady=20)

        # 状态显示
        self.status_label = ttk.Label(main_frame, text="状态: 未启动", foreground="red")
        self.status_label.grid(row=4, column=0, columnspan=2, pady=10)

        # 日志显示
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="5")
        log_frame.grid(row=5, column=0, columnspan=2, pady=10, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.log_text = tk.Text(log_frame, height=8, width=50)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

    def setup_window_settings_tab(self):
        """设置窗口句柄配置选项卡"""
        # 窗口设置选项卡
        window_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(window_frame, text="窗口设置")

        # 标题
        title_label = ttk.Label(window_frame, text="MuMu模拟器窗口设置", font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # 窗口句柄设置
        settings_frame = ttk.LabelFrame(window_frame, text="窗口句柄设置", padding="10")
        settings_frame.grid(row=1, column=0, columnspan=3, pady=10, sticky=(tk.W, tk.E))

        # 主窗口设置
        ttk.Label(settings_frame, text="主窗口句柄:").grid(row=0, column=0, padx=(0, 10), sticky=tk.W)
        self.main_hwnd_var = tk.StringVar(value=str(self.mumu_hwnd))
        ttk.Entry(settings_frame, textvariable=self.main_hwnd_var, width=15).grid(row=0, column=1, padx=(0, 10))

        ttk.Label(settings_frame, text="类名:").grid(row=0, column=2, padx=(10, 5), sticky=tk.W)
        self.main_class_var = tk.StringVar(value="Qt5156QWindowIcon")
        ttk.Entry(settings_frame, textvariable=self.main_class_var, width=20).grid(row=0, column=3, padx=(0, 10))

        # 子窗口设置
        ttk.Label(settings_frame, text="子窗口句柄:").grid(row=1, column=0, padx=(0, 10), sticky=tk.W)
        self.child_hwnd_var = tk.StringVar(value=str(self.mumu_child_hwnd))
        ttk.Entry(settings_frame, textvariable=self.child_hwnd_var, width=15).grid(row=1, column=1, padx=(0, 10))

        ttk.Label(settings_frame, text="类名:").grid(row=1, column=2, padx=(10, 5), sticky=tk.W)
        self.child_class_var = tk.StringVar(value="Qt5156QWindowIcon")
        ttk.Entry(settings_frame, textvariable=self.child_class_var, width=20).grid(row=1, column=3, padx=(0, 10))

        # 游戏窗口设置
        ttk.Label(settings_frame, text="游戏窗口句柄:").grid(row=2, column=0, padx=(0, 10), sticky=tk.W)
        self.game_hwnd_var = tk.StringVar(value=str(self.mumu_game_hwnd))
        ttk.Entry(settings_frame, textvariable=self.game_hwnd_var, width=15).grid(row=2, column=1, padx=(0, 10))

        ttk.Label(settings_frame, text="类名:").grid(row=2, column=2, padx=(10, 5), sticky=tk.W)
        self.game_class_var = tk.StringVar(value="nemuwin")
        ttk.Entry(settings_frame, textvariable=self.game_class_var, width=20).grid(row=2, column=3, padx=(0, 10))

        # 按钮框架
        button_frame = ttk.Frame(window_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=20)

        ttk.Button(button_frame, text="扫描窗口", command=self.scan_windows).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="应用设置", command=self.apply_window_settings).grid(row=0, column=1, padx=5)
        ttk.Button(button_frame, text="测试连接", command=self.test_window_connection).grid(row=0, column=2, padx=5)

        # 窗口列表显示
        list_frame = ttk.LabelFrame(window_frame, text="扫描到的窗口", padding="10")
        list_frame.grid(row=3, column=0, columnspan=3, pady=10, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 创建树形视图显示窗口信息
        columns = ("句柄", "标题", "类名")
        self.window_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=10)

        for col in columns:
            self.window_tree.heading(col, text=col)
            self.window_tree.column(col, width=150)

        # 添加滚动条
        tree_scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.window_tree.yview)
        self.window_tree.configure(yscrollcommand=tree_scrollbar.set)

        self.window_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tree_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 双击事件绑定
        self.window_tree.bind("<Double-1>", self.on_window_select)

        # 配置网格权重
        window_frame.columnconfigure(1, weight=1)
        window_frame.rowconfigure(3, weight=1)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

    def setup_image_test_tab(self):
        """设置图像测试选项卡"""
        # 图像测试选项卡
        test_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(test_frame, text="图像测试")

        # 标题
        title_label = ttk.Label(test_frame, text="图像识别测试", font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 测试控制框架
        control_frame = ttk.LabelFrame(test_frame, text="测试控制", padding="10")
        control_frame.grid(row=1, column=0, columnspan=2, pady=10, sticky=(tk.W, tk.E))

        ttk.Button(control_frame, text="捕获游戏窗口", command=self.capture_game_window).grid(row=0, column=0, padx=5)
        ttk.Button(control_frame, text="测试图像识别", command=self.test_image_recognition).grid(row=0, column=1, padx=5)
        ttk.Button(control_frame, text="测试窗口连接", command=self.test_all_windows).grid(row=0, column=2, padx=5)
        ttk.Button(control_frame, text="保存调试截图", command=self.save_debug_screenshots).grid(row=0, column=3, padx=5)

        # 测试结果显示
        result_frame = ttk.LabelFrame(test_frame, text="测试结果", padding="10")
        result_frame.grid(row=2, column=0, columnspan=2, pady=10, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.test_result_text = tk.Text(result_frame, height=15, width=80)
        test_scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.test_result_text.yview)
        self.test_result_text.configure(yscrollcommand=test_scrollbar.set)

        self.test_result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        test_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 配置网格权重
        test_frame.columnconfigure(1, weight=1)
        test_frame.rowconfigure(2, weight=1)
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        
    def load_images(self):
        """加载所有图片文件"""
        self.log("正在加载图片文件...")
        
        # 定义图片文件夹和对应的页面
        folders = {
            "main": "Main page",
            "map": "Map page", 
            "difficulty": "Level difficulty page",
            "role": "Select a role",
            "game": "Game page",
            "checkout": "Checkout page",
            "enemy": "Game page/enemy"
        }
        
        for category, folder in folders.items():
            self.images[category] = {}
            if os.path.exists(folder):
                for filename in os.listdir(folder):
                    if filename.lower().endswith(('.bmp', '.png', '.jpg', '.jpeg')):
                        filepath = os.path.join(folder, filename)
                        try:
                            # 使用OpenCV加载图片
                            img = cv2.imread(filepath)
                            if img is not None:
                                self.images[category][filename] = img
                                self.log(f"已加载: {filepath}")
                        except Exception as e:
                            self.log(f"加载图片失败: {filepath} - {str(e)}")
        
        self.log(f"图片加载完成，共加载 {sum(len(imgs) for imgs in self.images.values())} 张图片")
        
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists("config.json"):
                with open("config.json", "r", encoding="utf-8") as f:
                    config = json.load(f)
                    self.main_page_count = config.get("main_page_count", 0)
                    self.hotkey = config.get("hotkey", "F1")

                    # 加载窗口句柄配置
                    window_config = config.get("window_handles", {})
                    self.mumu_hwnd = window_config.get("mumu_main", 854566)
                    self.mumu_child_hwnd = window_config.get("mumu_child", 66862)
                    self.mumu_game_hwnd = window_config.get("mumu_game", 918834)

                    # 更新UI变量
                    if hasattr(self, 'hotkey_var'):
                        self.hotkey_var.set(self.hotkey)
                    if hasattr(self, 'main_hwnd_var'):
                        self.main_hwnd_var.set(str(self.mumu_hwnd))
                        self.child_hwnd_var.set(str(self.mumu_child_hwnd))
                        self.game_hwnd_var.set(str(self.mumu_game_hwnd))

                    self.update_count_display()
        except Exception as e:
            self.log(f"加载配置失败: {str(e)}")

    def save_config(self):
        """保存配置文件"""
        try:
            config = {
                "main_page_count": self.main_page_count,
                "hotkey": self.hotkey,
                "window_handles": {
                    "mumu_main": self.mumu_hwnd,
                    "mumu_child": self.mumu_child_hwnd,
                    "mumu_game": self.mumu_game_hwnd
                }
            }
            with open("config.json", "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log(f"保存配置失败: {str(e)}")
            
    def log(self, message):
        """添加日志信息"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        # 在主线程中更新UI
        self.root.after(0, lambda: self._update_log(log_message))
        
    def _update_log(self, message):
        """更新日志显示"""
        self.log_text.insert(tk.END, message)
        self.log_text.see(tk.END)
        
    def update_count_display(self):
        """更新进入主页面次数显示"""
        self.count_label.config(text=f"当前进入主页面的次数为 {self.main_page_count} 次")
        
    def setup_hotkey(self):
        """设置热键"""
        try:
            keyboard.unhook_all()  # 清除之前的热键
            keyboard.add_hotkey(self.hotkey, self.toggle_automation)
            self.log(f"热键 {self.hotkey} 设置成功")
        except Exception as e:
            self.log(f"设置热键失败: {str(e)}")
            
    def set_hotkey(self):
        """设置新的热键"""
        new_hotkey = self.hotkey_var.get().strip()
        if new_hotkey and new_hotkey != self.hotkey:
            self.hotkey = new_hotkey
            self.setup_hotkey()
            self.save_config()
            
    def toggle_automation(self):
        """切换自动化状态"""
        if self.is_running:
            self.stop_automation()
        else:
            self.start_automation()
            
    def start_automation(self):
        """启动自动化"""
        if not self.is_running:
            self.is_running = True
            self.start_button.config(text="停止")
            self.status_label.config(text="状态: 运行中", foreground="green")
            self.log("自动化已启动")
            
            # 重置状态变量
            self.current_page = "unknown"
            self.last_skill_time = 0
            self.star_placed = False
            self.guan_ping_placed = False
            self.monitoring_baozi = True
            
            # 启动自动化线程
            self.thread = threading.Thread(target=self.automation_loop, daemon=True)
            self.thread.start()
            
    def stop_automation(self):
        """停止自动化"""
        if self.is_running:
            self.is_running = False
            self.start_button.config(text="启动")
            self.status_label.config(text="状态: 已停止", foreground="red")
            self.log("自动化已停止")
            self.save_config()

    # ==================== 窗口设置功能 ====================

    def scan_windows(self):
        """扫描所有窗口"""
        self.test_log("正在扫描窗口...")

        # 清空现有列表
        for item in self.window_tree.get_children():
            self.window_tree.delete(item)

        windows = []

        def enum_windows_callback(hwnd, windows_list):
            if win32gui.IsWindowVisible(hwnd):
                try:
                    window_text = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)

                    if window_text or 'mumu' in class_name.lower() or 'qt' in class_name.lower() or 'nemu' in class_name.lower():
                        windows_list.append({
                            'hwnd': hwnd,
                            'title': window_text,
                            'class': class_name
                        })
                except:
                    pass

        try:
            win32gui.EnumWindows(enum_windows_callback, windows)

            # 添加到树形视图
            for window in windows:
                self.window_tree.insert("", "end", values=(
                    window['hwnd'],
                    window['title'][:50] if window['title'] else "(无标题)",
                    window['class']
                ))

            self.test_log(f"扫描完成，找到 {len(windows)} 个窗口")

        except Exception as e:
            self.test_log(f"扫描窗口失败: {str(e)}")

    def on_window_select(self, event):
        """窗口选择事件"""
        selection = self.window_tree.selection()
        if selection:
            item = self.window_tree.item(selection[0])
            values = item['values']

            # 弹出选择对话框
            choice = messagebox.askyesnocancel(
                "选择窗口类型",
                f"选择的窗口:\n句柄: {values[0]}\n标题: {values[1]}\n类名: {values[2]}\n\n"
                "请选择这是哪种类型的窗口:\n"
                "是 - 主窗口\n"
                "否 - 子窗口\n"
                "取消 - 游戏窗口"
            )

            if choice is True:  # 主窗口
                self.main_hwnd_var.set(str(values[0]))
                self.main_class_var.set(str(values[2]))
            elif choice is False:  # 子窗口
                self.child_hwnd_var.set(str(values[0]))
                self.child_class_var.set(str(values[2]))
            elif choice is None:  # 游戏窗口
                self.game_hwnd_var.set(str(values[0]))
                self.game_class_var.set(str(values[2]))

    def apply_window_settings(self):
        """应用窗口设置"""
        try:
            self.mumu_hwnd = int(self.main_hwnd_var.get())
            self.mumu_child_hwnd = int(self.child_hwnd_var.get())
            self.mumu_game_hwnd = int(self.game_hwnd_var.get())

            self.test_log("窗口设置已应用")
            self.save_config()

        except ValueError:
            messagebox.showerror("错误", "请输入有效的窗口句柄（数字）")

    def test_window_connection(self):
        """测试窗口连接"""
        self.test_log("测试窗口连接...")

        # 测试主窗口
        try:
            if win32gui.IsWindow(self.mumu_hwnd):
                title = win32gui.GetWindowText(self.mumu_hwnd)
                class_name = win32gui.GetClassName(self.mumu_hwnd)
                self.test_log(f"✓ 主窗口连接成功 - 标题: {title}, 类名: {class_name}")
            else:
                self.test_log("✗ 主窗口连接失败 - 窗口不存在")
        except Exception as e:
            self.test_log(f"✗ 主窗口连接失败 - {str(e)}")

        # 测试子窗口
        try:
            if win32gui.IsWindow(self.mumu_child_hwnd):
                title = win32gui.GetWindowText(self.mumu_child_hwnd)
                class_name = win32gui.GetClassName(self.mumu_child_hwnd)
                self.test_log(f"✓ 子窗口连接成功 - 标题: {title}, 类名: {class_name}")
            else:
                self.test_log("✗ 子窗口连接失败 - 窗口不存在")
        except Exception as e:
            self.test_log(f"✗ 子窗口连接失败 - {str(e)}")

        # 测试游戏窗口
        try:
            if win32gui.IsWindow(self.mumu_game_hwnd):
                title = win32gui.GetWindowText(self.mumu_game_hwnd)
                class_name = win32gui.GetClassName(self.mumu_game_hwnd)
                self.test_log(f"✓ 游戏窗口连接成功 - 标题: {title}, 类名: {class_name}")
            else:
                self.test_log("✗ 游戏窗口连接失败 - 窗口不存在")
        except Exception as e:
            self.test_log(f"✗ 游戏窗口连接失败 - {str(e)}")

    # ==================== 图像测试功能 ====================

    def test_log(self, message):
        """测试日志输出"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"

        # 在主线程中更新UI
        self.root.after(0, lambda: self._update_test_log(log_message))

    def _update_test_log(self, message):
        """更新测试日志显示"""
        self.test_result_text.insert(tk.END, message)
        self.test_result_text.see(tk.END)

    def capture_game_window(self):
        """捕获游戏窗口"""
        self.test_log("正在捕获游戏窗口...")

        screenshot = self.capture_window(self.mumu_game_hwnd)
        if screenshot is not None:
            cv2.imwrite("debug_game_window.png", screenshot)
            self.test_log(f"✓ 游戏窗口捕获成功，尺寸: {screenshot.shape}")
            self.test_log("截图已保存为 debug_game_window.png")
        else:
            self.test_log("✗ 游戏窗口捕获失败")

    def test_all_windows(self):
        """测试所有窗口"""
        self.test_log("测试所有窗口截图功能...")

        windows = [
            ("主窗口", self.mumu_hwnd),
            ("子窗口", self.mumu_child_hwnd),
            ("游戏窗口", self.mumu_game_hwnd)
        ]

        for name, hwnd in windows:
            screenshot = self.capture_window(hwnd)
            if screenshot is not None:
                filename = f"debug_{name}.png"
                cv2.imwrite(filename, screenshot)
                self.test_log(f"✓ {name}截图成功，尺寸: {screenshot.shape}，保存为: {filename}")
            else:
                self.test_log(f"✗ {name}截图失败")

    def test_image_recognition(self):
        """测试图像识别功能"""
        self.test_log("开始图像识别测试...")

        # 捕获游戏窗口截图
        screenshot = self.capture_window(self.mumu_game_hwnd)
        if screenshot is None:
            self.test_log("✗ 无法捕获游戏窗口，测试终止")
            return

        self.test_log(f"✓ 游戏窗口捕获成功，尺寸: {screenshot.shape}")

        # 测试各个文件夹的图片识别
        folders = {
            "主页面": "Main page",
            "选择地图": "Map page",
            "选择难度": "Level difficulty page",
            "选择角色": "Select a role",
            "战斗页面": "Game page",
            "结算页面": "Checkout page"
        }

        total_images = 0
        matched_images = 0

        for folder_name, folder_path in folders.items():
            self.test_log(f"\n测试 {folder_name} 文件夹:")

            if not os.path.exists(folder_path):
                self.test_log(f"  ✗ 文件夹不存在: {folder_path}")
                continue

            files = [f for f in os.listdir(folder_path) if f.lower().endswith(('.bmp', '.png', '.jpg', '.jpeg'))]

            if not files:
                self.test_log(f"  ✗ 文件夹为空: {folder_path}")
                continue

            for filename in files:
                filepath = os.path.join(folder_path, filename)
                total_images += 1

                try:
                    template = cv2.imread(filepath)
                    if template is not None:
                        match = self.find_image_in_screenshot(screenshot, template, threshold=0.7)
                        if match:
                            matched_images += 1
                            self.test_log(f"  ✓ {filename}: 匹配成功 - 位置({match[0]}, {match[1]}), 相似度: {match[2]:.3f}")
                        else:
                            self.test_log(f"  ✗ {filename}: 未找到匹配")
                    else:
                        self.test_log(f"  ! {filename}: 无法加载图片")
                except Exception as e:
                    self.test_log(f"  ! {filename}: 处理失败 - {str(e)}")

        # 测试敌兵识别
        self.test_log(f"\n测试敌兵识别:")
        enemy_folder = "Game page/enemy"
        if os.path.exists(enemy_folder):
            enemy_files = [f for f in os.listdir(enemy_folder) if f.lower().endswith(('.bmp', '.png', '.jpg', '.jpeg'))]

            # 定义技能释放区域
            skill_region = screenshot[434:593, 658:1012]

            enemy_matched = 0
            for filename in enemy_files:
                filepath = os.path.join(enemy_folder, filename)
                total_images += 1

                try:
                    enemy_template = cv2.imread(filepath)
                    if enemy_template is not None:
                        match = self.find_image_in_screenshot(skill_region, enemy_template, threshold=0.7)
                        if match:
                            enemy_matched += 1
                            matched_images += 1
                            self.test_log(f"  ✓ {filename}: 在技能区域发现敌兵 - 位置({match[0]}, {match[1]}), 相似度: {match[2]:.3f}")
                        else:
                            # 也在全屏幕中测试
                            full_match = self.find_image_in_screenshot(screenshot, enemy_template, threshold=0.7)
                            if full_match:
                                self.test_log(f"  ~ {filename}: 在全屏幕发现敌兵 - 位置({full_match[0]}, {full_match[1]}), 相似度: {full_match[2]:.3f}")
                            else:
                                self.test_log(f"  ✗ {filename}: 未发现敌兵")
                except Exception as e:
                    self.test_log(f"  ! {filename}: 处理失败 - {str(e)}")

            self.test_log(f"敌兵识别结果: {enemy_matched}/{len(enemy_files)} 个敌兵被识别")
        else:
            self.test_log("  ✗ 敌兵文件夹不存在")

        # 测试总结
        self.test_log(f"\n=== 测试总结 ===")
        self.test_log(f"总图片数: {total_images}")
        self.test_log(f"匹配成功: {matched_images}")
        self.test_log(f"匹配率: {matched_images/total_images*100:.1f}%" if total_images > 0 else "匹配率: 0%")

    def save_debug_screenshots(self):
        """保存调试截图"""
        self.test_log("保存调试截图...")

        # 捕获游戏窗口截图
        screenshot = self.capture_window(self.mumu_game_hwnd)
        if screenshot is None:
            self.test_log("✗ 无法捕获游戏窗口")
            return

        # 保存完整截图
        cv2.imwrite("debug_full_screenshot.png", screenshot)
        self.test_log("✓ 完整截图保存为: debug_full_screenshot.png")

        # 保存各个重要区域的截图
        regions = {
            "包子数量区域": (183, 68, 287, 106),
            "星彩放置区域": (689, 225, 760, 295),
            "关平放置区域": (480, 253, 566, 326),
            "技能释放区域": (658, 434, 1012, 593)
        }

        for region_name, (x1, y1, x2, y2) in regions.items():
            try:
                region_img = screenshot[y1:y2, x1:x2]
                filename = f"debug_{region_name}.png"
                cv2.imwrite(filename, region_img)
                self.test_log(f"✓ {region_name}截图保存为: {filename}")
            except Exception as e:
                self.test_log(f"✗ {region_name}截图保存失败: {str(e)}")

    def capture_window(self, hwnd):
        """捕获指定窗口的截图"""
        try:
            # 获取窗口矩形
            rect = win32gui.GetWindowRect(hwnd)
            width = rect[2] - rect[0]
            height = rect[3] - rect[1]

            # 获取窗口设备上下文
            hwndDC = win32gui.GetWindowDC(hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()

            # 创建位图对象
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)

            # 截图
            result = windll.user32.PrintWindow(hwnd, saveDC.GetSafeHdc(), 3)

            if result:
                # 获取位图信息
                bmpstr = saveBitMap.GetBitmapBits(True)

                # 转换为numpy数组
                img = np.frombuffer(bmpstr, dtype='uint8')
                img.shape = (height, width, 4)

                # 转换为BGR格式（OpenCV格式）
                img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)

                # 清理资源
                win32gui.DeleteObject(saveBitMap.GetHandle())
                saveDC.DeleteDC()
                mfcDC.DeleteDC()
                win32gui.ReleaseDC(hwnd, hwndDC)

                return img
            else:
                self.log(f"截图失败，窗口句柄: {hwnd}")
                return None

        except Exception as e:
            self.log(f"捕获窗口失败: {str(e)}")
            return None

    def find_image_in_screenshot(self, screenshot, template, threshold=0.8):
        """在截图中查找模板图片"""
        try:
            if screenshot is None or template is None:
                return None

            # 使用模板匹配
            result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)

            if max_val >= threshold:
                # 返回匹配位置的中心点
                h, w = template.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                return (center_x, center_y, max_val)
            else:
                return None

        except Exception as e:
            self.log(f"图像匹配失败: {str(e)}")
            return None

    def click_at_position(self, x, y):
        """在指定位置点击鼠标"""
        try:
            # 将坐标转换为相对于游戏窗口的坐标
            lParam = win32api.MAKELONG(x, y)

            # 发送鼠标按下和释放消息
            win32api.SendMessage(self.mumu_game_hwnd, win32con.WM_LBUTTONDOWN, win32con.MK_LBUTTON, lParam)
            time.sleep(0.05)  # 短暂延迟
            win32api.SendMessage(self.mumu_game_hwnd, win32con.WM_LBUTTONUP, 0, lParam)

            self.log(f"点击位置: ({x}, {y})")

        except Exception as e:
            self.log(f"点击失败: {str(e)}")

    def drag_mouse(self, start_x, start_y, end_x, end_y, duration=0.5):
        """拖拽鼠标"""
        try:
            # 开始拖拽
            start_lParam = win32api.MAKELONG(start_x, start_y)
            win32api.SendMessage(self.mumu_game_hwnd, win32con.WM_LBUTTONDOWN, win32con.MK_LBUTTON, start_lParam)

            # 计算拖拽步数
            steps = int(duration * 60)  # 60fps
            if steps < 1:
                steps = 1

            for i in range(steps):
                progress = (i + 1) / steps
                current_x = int(start_x + (end_x - start_x) * progress)
                current_y = int(start_y + (end_y - start_y) * progress)

                current_lParam = win32api.MAKELONG(current_x, current_y)
                win32api.SendMessage(self.mumu_game_hwnd, win32con.WM_MOUSEMOVE, win32con.MK_LBUTTON, current_lParam)
                time.sleep(duration / steps)

            # 结束拖拽
            end_lParam = win32api.MAKELONG(end_x, end_y)
            win32api.SendMessage(self.mumu_game_hwnd, win32con.WM_LBUTTONUP, 0, end_lParam)

            self.log(f"拖拽: ({start_x}, {start_y}) -> ({end_x}, {end_y})")

        except Exception as e:
            self.log(f"拖拽失败: {str(e)}")

    def use_adb_swipe(self, start_x, start_y, end_x, end_y, duration=500):
        """使用ADB命令进行滑动操作"""
        try:
            cmd = f"adb shell input swipe {start_x} {start_y} {end_x} {end_y} {duration}"
            subprocess.run(cmd, shell=True, capture_output=True)
            self.log(f"ADB滑动: ({start_x}, {start_y}) -> ({end_x}, {end_y})")
        except Exception as e:
            self.log(f"ADB滑动失败: {str(e)}")

    def read_text_region(self, screenshot, x1, y1, x2, y2):
        """读取指定区域的文本（数字识别）"""
        try:
            if screenshot is None:
                return ""

            # 裁剪指定区域
            region = screenshot[y1:y2, x1:x2]

            # 转换为灰度图
            gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)

            # 二值化处理
            _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)

            # 尝试使用pytesseract进行OCR识别
            try:
                import pytesseract
                # 配置OCR只识别数字
                config = '--psm 8 -c tessedit_char_whitelist=0123456789'
                text = pytesseract.image_to_string(binary, config=config).strip()

                # 验证结果是否为数字
                if text.isdigit():
                    return text
                else:
                    # 如果OCR失败，返回默认值
                    return "0"

            except ImportError:
                # 如果没有安装pytesseract，使用简单的模板匹配
                self.log("警告: 未安装pytesseract，使用简化的数字识别")
                return self.simple_number_recognition(binary)

        except Exception as e:
            self.log(f"读取文本区域失败: {str(e)}")
            return "0"

    def simple_number_recognition(self, binary_image):
        """简单的数字识别（基于图像特征）"""
        try:
            # 这里可以实现基于图像特征的简单数字识别
            # 为了演示，我们返回一个基于图像亮度的估算值
            mean_brightness = np.mean(binary_image)

            # 根据亮度估算数字（这是一个非常简化的方法）
            if mean_brightness > 200:
                return "200"
            elif mean_brightness > 150:
                return "180"
            elif mean_brightness > 100:
                return "160"
            else:
                return "0"

        except Exception as e:
            self.log(f"简单数字识别失败: {str(e)}")
            return "0"

    def automation_loop(self):
        """主自动化循环"""
        self.log("开始自动化循环")

        while self.is_running:
            try:
                # 捕获游戏窗口截图
                screenshot = self.capture_window(self.mumu_game_hwnd)
                if screenshot is None:
                    self.log("无法捕获游戏窗口，请检查MuMu模拟器是否正常运行")
                    time.sleep(2)
                    continue

                # 识别当前页面并执行相应操作
                self.process_current_page(screenshot)

                # 短暂延迟避免过度占用CPU
                time.sleep(0.5)

            except Exception as e:
                self.log(f"自动化循环错误: {str(e)}")
                time.sleep(1)

        self.log("自动化循环结束")

    def process_current_page(self, screenshot):
        """处理当前页面"""
        # 按优先级检查各个页面
        if self.check_and_process_main_page(screenshot):
            return
        elif self.check_and_process_map_page(screenshot):
            return
        elif self.check_and_process_difficulty_page(screenshot):
            return
        elif self.check_and_process_role_page(screenshot):
            return
        elif self.check_and_process_game_page(screenshot):
            return
        elif self.check_and_process_checkout_page(screenshot):
            return
        else:
            # 未识别的页面
            if self.current_page != "unknown":
                self.current_page = "unknown"
                self.log("当前页面未识别")

    def check_and_process_main_page(self, screenshot):
        """检查并处理主页面"""
        if "main" not in self.images or "主页面.bmp" not in self.images["main"]:
            return False

        # 检查是否为主页面
        main_template = self.images["main"]["主页面.bmp"]
        match = self.find_image_in_screenshot(screenshot, main_template, threshold=0.7)

        if match:
            if self.current_page != "main":
                self.current_page = "main"
                self.main_page_count += 1
                self.root.after(0, self.update_count_display)
                self.log(f"进入主页面，当前次数: {self.main_page_count}")

            # 查找并点击选择地图按钮
            if "选择地图.bmp" in self.images["main"]:
                map_template = self.images["main"]["选择地图.bmp"]
                map_match = self.find_image_in_screenshot(screenshot, map_template, threshold=0.8)

                if map_match:
                    self.click_at_position(map_match[0], map_match[1])
                    self.log("点击选择地图按钮")
                    time.sleep(1)

            return True

        return False

    def check_and_process_map_page(self, screenshot):
        """检查并处理选择地图页面"""
        if "map" not in self.images or "选择地图主页面.bmp" not in self.images["map"]:
            return False

        # 检查是否为选择地图页面
        map_main_template = self.images["map"]["选择地图主页面.bmp"]
        match = self.find_image_in_screenshot(screenshot, map_main_template, threshold=0.7)

        if match:
            if self.current_page != "map":
                self.current_page = "map"
                self.log("进入选择地图页面")

            # 查找并点击选择关卡按钮
            if "选择关卡.bmp" in self.images["map"]:
                level_template = self.images["map"]["选择关卡.bmp"]
                level_match = self.find_image_in_screenshot(screenshot, level_template, threshold=0.8)

                if level_match:
                    self.click_at_position(level_match[0], level_match[1])
                    self.log("点击选择关卡按钮")
                    time.sleep(1)

            return True

        return False

    def check_and_process_difficulty_page(self, screenshot):
        """检查并处理选择难度页面"""
        if "difficulty" not in self.images or "选择难度.bmp" not in self.images["difficulty"]:
            return False

        # 检查是否为选择难度页面
        difficulty_template = self.images["difficulty"]["选择难度.bmp"]
        match = self.find_image_in_screenshot(screenshot, difficulty_template, threshold=0.7)

        if match:
            if self.current_page != "difficulty":
                self.current_page = "difficulty"
                self.log("进入选择难度页面")

            # 检查当前难度设置
            self.process_difficulty_selection(screenshot)
            return True

        return False

    def process_difficulty_selection(self, screenshot):
        """处理难度选择逻辑"""
        if "简单难度.bmp" not in self.images["difficulty"] or "困难难度.bmp" not in self.images["difficulty"]:
            return

        # 检查当前难度位置的文本
        simple_template = self.images["difficulty"]["简单难度.bmp"]
        hard_template = self.images["difficulty"]["困难难度.bmp"]

        # 查找简单难度位置
        simple_match = self.find_image_in_screenshot(screenshot, simple_template, threshold=0.8)

        if simple_match:
            # 检查该位置是否显示困难难度
            hard_match = self.find_image_in_screenshot(screenshot, hard_template, threshold=0.8)

            if hard_match:
                # 已经是困难难度，点击进入战场
                if "进入战场按钮.bmp" in self.images["difficulty"]:
                    enter_template = self.images["difficulty"]["进入战场按钮.bmp"]
                    enter_match = self.find_image_in_screenshot(screenshot, enter_template, threshold=0.8)

                    if enter_match:
                        self.click_at_position(enter_match[0], enter_match[1])
                        self.log("点击进入战场按钮")
                        time.sleep(1)
            else:
                # 不是困难难度，点击选择箭头切换
                if "选择箭头.bmp" in self.images["difficulty"]:
                    arrow_template = self.images["difficulty"]["选择箭头.bmp"]
                    arrow_match = self.find_image_in_screenshot(screenshot, arrow_template, threshold=0.8)

                    if arrow_match:
                        self.click_at_position(arrow_match[0], arrow_match[1])
                        self.log("点击选择箭头切换难度")
                        time.sleep(0.5)

    def check_and_process_role_page(self, screenshot):
        """检查并处理选择角色页面"""
        if "role" not in self.images or "选择人物页面.bmp" not in self.images["role"]:
            return False

        # 检查是否为选择角色页面
        role_template = self.images["role"]["选择人物页面.bmp"]
        match = self.find_image_in_screenshot(screenshot, role_template, threshold=0.7)

        if match:
            if self.current_page != "role":
                self.current_page = "role"
                self.log("进入选择角色页面")

            # 查找并点击开始战斗按钮
            if "开始战斗按钮.bmp" in self.images["role"]:
                battle_template = self.images["role"]["开始战斗按钮.bmp"]
                battle_match = self.find_image_in_screenshot(screenshot, battle_template, threshold=0.8)

                if battle_match:
                    self.click_at_position(battle_match[0], battle_match[1])
                    self.log("点击开始战斗按钮")
                    time.sleep(2)

            return True

        return False

    def check_and_process_game_page(self, screenshot):
        """检查并处理战斗页面"""
        if "game" not in self.images:
            return False

        # 检查是否为战斗页面
        if "开始战斗页面.bmp" in self.images["game"]:
            battle_template = self.images["game"]["开始战斗页面.bmp"]
            match = self.find_image_in_screenshot(screenshot, battle_template, threshold=0.7)

            if match:
                if self.current_page != "game":
                    self.current_page = "game"
                    self.log("进入战斗页面")
                    # 重置战斗页面状态
                    self.star_placed = False
                    self.guan_ping_placed = False
                    self.monitoring_baozi = True
                    self.last_skill_time = 0

                # 执行初始战斗设置
                self.process_initial_battle_setup(screenshot)
                return True

        # 检查其他战斗页面状态
        if self.current_page == "game":
            self.process_battle_operations(screenshot)
            return True

        return False

    def process_initial_battle_setup(self, screenshot):
        """处理初始战斗设置"""
        self.log("执行初始战斗设置")

        # 1. 拖动屏幕
        self.use_adb_swipe(510, 290, 290, 290, 500)
        time.sleep(1)

        # 2. 点击暂停键
        self.click_at_position(170, 500)
        self.log("点击暂停键")
        time.sleep(1)

        # 3. 放置角色
        self.place_initial_characters()

        # 4. 设置游戏速度
        self.click_at_position(72, 505)
        self.log("设置游戏速度")
        time.sleep(0.5)

        # 5. 再次点击暂停键开始游戏
        self.click_at_position(170, 500)
        self.log("开始游戏")

    def place_initial_characters(self):
        """放置初始角色"""
        # 放置许褚
        self.drag_mouse(640, 473, 391, 293, 0.5)
        self.log("放置许褚")
        time.sleep(0.5)

        # 放置荀彧
        self.drag_mouse(536, 476, 428, 283, 0.5)
        self.log("放置荀彧")
        time.sleep(0.5)

        # 放置黄忠
        self.drag_mouse(836, 481, 424, 260, 0.5)
        self.log("放置黄忠")
        time.sleep(0.5)

    def process_battle_operations(self, screenshot):
        """处理战斗中的操作"""
        current_time = time.time()

        # 监测包子数量（如果还在监测中）
        if self.monitoring_baozi:
            baozi_count = self.read_text_region(screenshot, 183, 68, 287, 106)
            try:
                baozi_num = int(baozi_count) if baozi_count.isdigit() else 0
            except:
                baozi_num = 0

            # 检查是否需要放置星彩
            if not self.star_placed and baozi_num >= 180:
                if self.check_star_placement_area(screenshot):
                    self.place_star_character()

            # 检查是否需要放置关平
            if not self.guan_ping_placed and baozi_num >= 160:
                if self.check_guan_ping_placement_area(screenshot):
                    self.place_guan_ping_character()

        # 监测技能释放区域
        self.monitor_skill_area(screenshot, current_time)

    def check_star_placement_area(self, screenshot):
        """检查星彩放置区域"""
        # 检查星彩放置区域 (689, 225, 760, 295)
        if "星彩人物.bmp" in self.images["game"]:
            star_template = self.images["game"]["星彩人物.bmp"]
            region = screenshot[225:295, 689:760]
            match = self.find_image_in_screenshot(region, star_template, threshold=0.8)
            return match is None  # 如果没有找到星彩，返回True表示可以放置
        return True

    def place_star_character(self):
        """放置星彩角色"""
        self.drag_mouse(719, 476, 644, 248, 0.5)
        self.log("放置星彩")
        self.star_placed = True
        time.sleep(0.5)

        # 点击合并按钮
        self.click_at_position(924, 505)
        self.log("点击合并按钮")

    def check_guan_ping_placement_area(self, screenshot):
        """检查关平放置区域"""
        # 检查关平放置区域 (480, 253, 566, 326)
        if "关平人物.bmp" in self.images["game"]:
            guan_ping_template = self.images["game"]["关平人物.bmp"]
            region = screenshot[253:326, 480:566]
            match = self.find_image_in_screenshot(region, guan_ping_template, threshold=0.8)
            return match is None  # 如果没有找到关平，返回True表示可以放置
        return True

    def place_guan_ping_character(self):
        """放置关平角色"""
        # 先点击合并按钮
        self.click_at_position(924, 505)
        self.log("点击合并按钮")
        time.sleep(0.5)

        # 放置关平
        self.drag_mouse(436, 480, 478, 303, 0.5)
        self.log("放置关平")
        self.guan_ping_placed = True
        time.sleep(0.5)

        # 再次点击合并按钮
        self.click_at_position(924, 505)
        self.log("点击合并按钮")

        # 停止监测包子数量
        self.monitoring_baozi = False
        self.log("停止监测包子数量")

    def monitor_skill_area(self, screenshot, current_time):
        """监测技能释放区域"""
        # 技能释放区域 (658, 434, 1012, 593)
        skill_region = screenshot[434:593, 658:1012]

        # 检查是否有敌兵在技能区域
        enemy_detected = False
        if "enemy" in self.images:
            for enemy_name, enemy_template in self.images["enemy"].items():
                match = self.find_image_in_screenshot(skill_region, enemy_template, threshold=0.7)
                if match:
                    enemy_detected = True
                    self.log(f"检测到敌兵: {enemy_name}")
                    break

        # 技能释放逻辑
        if enemy_detected:
            # 有敌兵时立即释放技能
            self.release_skill()
            self.last_skill_time = current_time
        elif current_time - self.last_skill_time >= 4.0:
            # 没有敌兵但距离上次释放技能超过4秒，也释放技能
            self.release_skill()
            self.last_skill_time = current_time

    def release_skill(self):
        """释放技能"""
        # 点击第一个技能位置
        self.click_at_position(610, 213)
        time.sleep(0.2)

        # 点击第二个技能位置
        self.click_at_position(604, 330)
        self.log("释放技能")

    def check_and_process_checkout_page(self, screenshot):
        """检查并处理结算页面"""
        if "checkout" not in self.images:
            return False

        # 检查结算页面1
        if "结算页面1.bmp" in self.images["checkout"]:
            checkout1_template = self.images["checkout"]["结算页面1.bmp"]
            match = self.find_image_in_screenshot(screenshot, checkout1_template, threshold=0.7)

            if match:
                if self.current_page != "checkout1":
                    self.current_page = "checkout1"
                    self.log("进入结算页面1")

                # 点击继续按钮
                self.click_continue_button(screenshot)
                return True

        # 检查结算页面2
        if "结算页面2.bmp" in self.images["checkout"]:
            checkout2_template = self.images["checkout"]["结算页面2.bmp"]
            match = self.find_image_in_screenshot(screenshot, checkout2_template, threshold=0.7)

            if match:
                if self.current_page != "checkout2":
                    self.current_page = "checkout2"
                    self.log("进入结算页面2")

                # 点击继续按钮
                self.click_continue_button(screenshot)
                return True

        return False

    def click_continue_button(self, screenshot):
        """点击继续按钮"""
        if "继续按钮.bmp" in self.images["checkout"]:
            continue_template = self.images["checkout"]["继续按钮.bmp"]
            continue_match = self.find_image_in_screenshot(screenshot, continue_template, threshold=0.8)

            if continue_match:
                self.click_at_position(continue_match[0], continue_match[1])
                self.log("点击继续按钮")
                time.sleep(1)

    def run(self):
        """运行应用程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log("程序被用户中断")
        finally:
            self.is_running = False
            keyboard.unhook_all()
            self.save_config()

if __name__ == "__main__":
    app = GameAutomation()
    app.run()
