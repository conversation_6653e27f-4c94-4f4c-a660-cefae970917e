import tkinter as tk
from tkinter import ttk, messagebox
import win32gui

def test_ui():
    """测试UI界面"""
    root = tk.Tk()
    root.title("游戏自动化助手 - 测试版")
    root.geometry("800x600")
    
    # 创建选项卡控件
    notebook = ttk.Notebook(root)
    notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 主功能选项卡
    main_frame = ttk.Frame(notebook, padding="10")
    notebook.add(main_frame, text="主要功能")
    
    ttk.Label(main_frame, text="游戏自动化助手", font=("Arial", 16, "bold")).pack(pady=20)
    ttk.Button(main_frame, text="启动", width=20).pack(pady=10)
    
    # 窗口设置选项卡
    window_frame = ttk.Frame(notebook, padding="10")
    notebook.add(window_frame, text="窗口设置")
    
    ttk.Label(window_frame, text="MuMu模拟器窗口设置", font=("Arial", 14, "bold")).pack(pady=20)
    
    settings_frame = ttk.LabelFrame(window_frame, text="窗口句柄设置", padding="10")
    settings_frame.pack(fill=tk.X, pady=10)
    
    # 主窗口设置
    main_hwnd_frame = ttk.Frame(settings_frame)
    main_hwnd_frame.pack(fill=tk.X, pady=5)
    ttk.Label(main_hwnd_frame, text="主窗口句柄:").pack(side=tk.LEFT)
    ttk.Entry(main_hwnd_frame, width=15).pack(side=tk.LEFT, padx=10)
    ttk.Label(main_hwnd_frame, text="类名:").pack(side=tk.LEFT, padx=(20, 5))
    ttk.Entry(main_hwnd_frame, width=20).pack(side=tk.LEFT)
    
    # 按钮
    button_frame = ttk.Frame(window_frame)
    button_frame.pack(pady=20)
    ttk.Button(button_frame, text="扫描窗口").pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="应用设置").pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="测试连接").pack(side=tk.LEFT, padx=5)
    
    # 图像测试选项卡
    test_frame = ttk.Frame(notebook, padding="10")
    notebook.add(test_frame, text="图像测试")
    
    ttk.Label(test_frame, text="图像识别测试", font=("Arial", 14, "bold")).pack(pady=20)
    
    test_button_frame = ttk.Frame(test_frame)
    test_button_frame.pack(pady=10)
    ttk.Button(test_button_frame, text="捕获游戏窗口").pack(side=tk.LEFT, padx=5)
    ttk.Button(test_button_frame, text="测试图像识别").pack(side=tk.LEFT, padx=5)
    
    # 测试结果显示
    result_frame = ttk.LabelFrame(test_frame, text="测试结果", padding="10")
    result_frame.pack(fill=tk.BOTH, expand=True, pady=10)
    
    test_text = tk.Text(result_frame, height=15)
    test_scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=test_text.yview)
    test_text.configure(yscrollcommand=test_scrollbar.set)
    
    test_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    test_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    root.mainloop()

if __name__ == "__main__":
    test_ui()
