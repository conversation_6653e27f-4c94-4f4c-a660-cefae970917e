import cv2
import numpy as np
import os
import win32gui
import win32ui
from ctypes import windll

def capture_window(hwnd):
    """捕获指定窗口的截图"""
    try:
        rect = win32gui.GetWindowRect(hwnd)
        width = rect[2] - rect[0]
        height = rect[3] - rect[1]
        
        hwndDC = win32gui.GetWindowDC(hwnd)
        mfcDC = win32ui.CreateDCFromHandle(hwndDC)
        saveDC = mfcDC.CreateCompatibleDC()
        
        saveBitMap = win32ui.CreateBitmap()
        saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
        saveDC.SelectObject(saveBitMap)
        
        result = windll.user32.PrintWindow(hwnd, saveDC.GetSafeHdc(), 3)
        
        if result:
            bmpinfo = saveBitMap.GetInfo()
            bmpstr = saveBitMap.GetBitmapBits(True)
            
            img = np.frombuffer(bmpstr, dtype='uint8')
            img.shape = (height, width, 4)
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
            
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(hwnd, hwndDC)
            
            return img
        else:
            print(f"截图失败，窗口句柄: {hwnd}")
            return None
            
    except Exception as e:
        print(f"捕获窗口失败: {str(e)}")
        return None

def find_image_in_screenshot(screenshot, template, threshold=0.8):
    """在截图中查找模板图片"""
    try:
        if screenshot is None or template is None:
            return None
            
        result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        
        if max_val >= threshold:
            h, w = template.shape[:2]
            center_x = max_loc[0] + w // 2
            center_y = max_loc[1] + h // 2
            return (center_x, center_y, max_val)
        else:
            return None
            
    except Exception as e:
        print(f"图像匹配失败: {str(e)}")
        return None

def test_image_recognition():
    """测试图像识别功能"""
    print("图像识别测试工具")
    print("=" * 50)
    
    # 获取窗口句柄
    hwnd = input("请输入MuMu游戏窗口句柄 (默认: 918834): ").strip()
    if not hwnd:
        hwnd = 918834
    else:
        hwnd = int(hwnd)
    
    print(f"使用窗口句柄: {hwnd}")
    
    # 捕获截图
    print("正在捕获游戏窗口截图...")
    screenshot = capture_window(hwnd)
    
    if screenshot is None:
        print("无法捕获窗口截图，请检查窗口句柄是否正确")
        return
    
    print(f"截图成功，尺寸: {screenshot.shape}")
    
    # 保存截图用于调试
    cv2.imwrite("debug_screenshot.png", screenshot)
    print("截图已保存为 debug_screenshot.png")
    
    # 测试各个文件夹的图片识别
    folders = {
        "主页面": "Main page",
        "选择地图": "Map page", 
        "选择难度": "Level difficulty page",
        "选择角色": "Select a role",
        "战斗页面": "Game page",
        "结算页面": "Checkout page"
    }
    
    print("\n开始测试图片识别...")
    print("-" * 50)
    
    for folder_name, folder_path in folders.items():
        print(f"\n测试 {folder_name} 文件夹:")
        
        if not os.path.exists(folder_path):
            print(f"  文件夹不存在: {folder_path}")
            continue
            
        files = [f for f in os.listdir(folder_path) if f.lower().endswith(('.bmp', '.png', '.jpg', '.jpeg'))]
        
        if not files:
            print(f"  文件夹为空: {folder_path}")
            continue
            
        for filename in files:
            filepath = os.path.join(folder_path, filename)
            try:
                template = cv2.imread(filepath)
                if template is not None:
                    match = find_image_in_screenshot(screenshot, template, threshold=0.7)
                    if match:
                        print(f"  ✓ {filename}: 匹配成功 - 位置({match[0]}, {match[1]}), 相似度: {match[2]:.3f}")
                    else:
                        print(f"  ✗ {filename}: 未找到匹配")
                else:
                    print(f"  ! {filename}: 无法加载图片")
            except Exception as e:
                print(f"  ! {filename}: 处理失败 - {str(e)}")
    
    # 测试敌兵识别
    print(f"\n测试敌兵识别:")
    enemy_folder = "Game page/enemy"
    if os.path.exists(enemy_folder):
        enemy_files = [f for f in os.listdir(enemy_folder) if f.lower().endswith(('.bmp', '.png', '.jpg', '.jpeg'))]
        
        # 定义技能释放区域
        skill_region = screenshot[434:593, 658:1012]
        cv2.imwrite("debug_skill_region.png", skill_region)
        print("技能区域截图已保存为 debug_skill_region.png")
        
        for filename in enemy_files:
            filepath = os.path.join(enemy_folder, filename)
            try:
                enemy_template = cv2.imread(filepath)
                if enemy_template is not None:
                    match = find_image_in_screenshot(skill_region, enemy_template, threshold=0.7)
                    if match:
                        print(f"  ✓ {filename}: 在技能区域发现敌兵 - 位置({match[0]}, {match[1]}), 相似度: {match[2]:.3f}")
                    else:
                        # 也在全屏幕中测试
                        full_match = find_image_in_screenshot(screenshot, enemy_template, threshold=0.7)
                        if full_match:
                            print(f"  ~ {filename}: 在全屏幕发现敌兵 - 位置({full_match[0]}, {full_match[1]}), 相似度: {full_match[2]:.3f}")
                        else:
                            print(f"  ✗ {filename}: 未发现敌兵")
            except Exception as e:
                print(f"  ! {filename}: 处理失败 - {str(e)}")
    else:
        print("  敌兵文件夹不存在")
    
    print("\n测试完成！")
    print("请查看生成的调试图片文件来验证识别结果。")

if __name__ == "__main__":
    try:
        test_image_recognition()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")
    
    input("\n按回车键退出...")
