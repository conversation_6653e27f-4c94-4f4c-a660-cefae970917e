"""
测试窗口点击和坐标转换问题
"""
import win32gui
import win32api
import win32con
import win32ui
import time
from ctypes import windll

def get_window_info(hwnd):
    """获取窗口详细信息"""
    try:
        title = win32gui.GetWindowText(hwnd)
        class_name = win32gui.GetClassName(hwnd)
        rect = win32gui.GetWindowRect(hwnd)
        client_rect = win32gui.GetClientRect(hwnd)
        
        print(f"窗口句柄: {hwnd}")
        print(f"窗口标题: {title}")
        print(f"窗口类名: {class_name}")
        print(f"窗口矩形: {rect} (左,上,右,下)")
        print(f"客户区矩形: {client_rect} (宽,高)")
        print(f"窗口大小: {rect[2]-rect[0]} x {rect[3]-rect[1]}")
        print(f"客户区大小: {client_rect[2]} x {client_rect[3]}")
        
        # 检查窗口是否可见
        is_visible = win32gui.IsWindowVisible(hwnd)
        print(f"窗口可见: {is_visible}")
        
        # 获取父窗口
        parent = win32gui.GetParent(hwnd)
        if parent:
            parent_title = win32gui.GetWindowText(parent)
            print(f"父窗口: {parent} ({parent_title})")
        
        print("-" * 50)
        
    except Exception as e:
        print(f"获取窗口信息失败: {e}")

def test_click_methods(hwnd, x, y):
    """测试不同的点击方法"""
    print(f"测试在窗口 {hwnd} 的坐标 ({x}, {y}) 点击...")
    
    # 方法1: SendMessage 到游戏窗口
    print("方法1: SendMessage 到游戏窗口")
    try:
        lParam = win32api.MAKELONG(x, y)
        win32api.SendMessage(hwnd, win32con.WM_LBUTTONDOWN, win32con.MK_LBUTTON, lParam)
        time.sleep(0.05)
        win32api.SendMessage(hwnd, win32con.WM_LBUTTONUP, 0, lParam)
        print("  ✓ SendMessage 执行成功")
    except Exception as e:
        print(f"  ✗ SendMessage 失败: {e}")
    
    time.sleep(1)
    
    # 方法2: PostMessage 到游戏窗口
    print("方法2: PostMessage 到游戏窗口")
    try:
        lParam = win32api.MAKELONG(x, y)
        win32api.PostMessage(hwnd, win32con.WM_LBUTTONDOWN, win32con.MK_LBUTTON, lParam)
        time.sleep(0.05)
        win32api.PostMessage(hwnd, win32con.WM_LBUTTONUP, 0, lParam)
        print("  ✓ PostMessage 执行成功")
    except Exception as e:
        print(f"  ✗ PostMessage 失败: {e}")
    
    time.sleep(1)

def convert_coordinates(hwnd, x, y):
    """转换坐标系统"""
    try:
        # 获取窗口矩形
        rect = win32gui.GetWindowRect(hwnd)
        client_rect = win32gui.GetClientRect(hwnd)
        
        print(f"原始坐标: ({x}, {y})")
        print(f"窗口矩形: {rect}")
        print(f"客户区矩形: {client_rect}")
        
        # 计算标题栏和边框的偏移
        border_x = (rect[2] - rect[0] - client_rect[2]) // 2
        border_y = rect[3] - rect[1] - client_rect[3] - border_x
        
        print(f"边框偏移: x={border_x}, y={border_y}")
        
        # 转换为客户区坐标
        client_x = x - border_x
        client_y = y - border_y
        
        print(f"客户区坐标: ({client_x}, {client_y})")
        
        return client_x, client_y
        
    except Exception as e:
        print(f"坐标转换失败: {e}")
        return x, y

def test_window_hierarchy():
    """测试窗口层级"""
    print("=== 测试MuMu窗口层级 ===")
    
    # 您提供的窗口句柄
    main_hwnd = 66824      # 主窗口
    child_hwnd = 66862     # 子窗口  
    game_hwnd = 918834     # 游戏窗口
    
    print("主窗口信息:")
    get_window_info(main_hwnd)
    
    print("子窗口信息:")
    get_window_info(child_hwnd)
    
    print("游戏窗口信息:")
    get_window_info(game_hwnd)
    
    # 测试坐标转换
    print("=== 测试坐标转换 ===")
    test_x, test_y = 640, 500  # 假设的点击坐标
    
    print("游戏窗口坐标转换:")
    client_x, client_y = convert_coordinates(game_hwnd, test_x, test_y)
    
    # 测试点击
    print("=== 测试点击方法 ===")
    print("请确保MuMu模拟器窗口可见，5秒后开始测试点击...")
    time.sleep(5)
    
    test_click_methods(game_hwnd, client_x, client_y)

def test_focus_and_click():
    """测试窗口焦点和点击"""
    game_hwnd = 918834
    
    print("=== 测试窗口焦点 ===")
    
    try:
        # 尝试设置窗口为前台窗口
        print("设置游戏窗口为前台...")
        win32gui.SetForegroundWindow(game_hwnd)
        time.sleep(0.5)
        
        # 检查当前前台窗口
        foreground = win32gui.GetForegroundWindow()
        print(f"当前前台窗口: {foreground}")
        
        if foreground == game_hwnd:
            print("✓ 游戏窗口已设为前台")
        else:
            print("✗ 游戏窗口未能设为前台")
            
            # 尝试通过父窗口设置
            parent = win32gui.GetParent(game_hwnd)
            if parent:
                print(f"尝试通过父窗口 {parent} 设置前台...")
                win32gui.SetForegroundWindow(parent)
                time.sleep(0.5)
        
        # 测试点击
        print("测试在前台窗口点击...")
        test_x, test_y = 640, 500
        client_x, client_y = convert_coordinates(game_hwnd, test_x, test_y)
        test_click_methods(game_hwnd, client_x, client_y)
        
    except Exception as e:
        print(f"窗口焦点测试失败: {e}")

if __name__ == "__main__":
    print("MuMu窗口点击测试工具")
    print("=" * 50)
    
    test_window_hierarchy()
    
    print("\n" + "=" * 50)
    test_focus_and_click()
    
    print("\n测试完成！请检查MuMu模拟器是否有反应。")
